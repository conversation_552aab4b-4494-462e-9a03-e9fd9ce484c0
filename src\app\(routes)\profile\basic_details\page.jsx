"use client";
import Address from "@/components/Profile/Address";
import { Dialog, Transition } from "@headlessui/react";
import { useEffect, useState, Fragment } from "react";
import { useFormik } from "formik";
import * as Yup from "yup";
import dynamic from "next/dynamic";
import axios from "axios";
import Select from "react-select";
import "@/style/customStyle.css";
const ReactQuill = dynamic(() => import("react-quill"), {
  ssr: false,
});
import { useRouter } from "next/navigation";

import {
  XMarkIcon,
  PencilSquareIcon,
  TrashIcon,
} from "@heroicons/react/24/solid";
import API from "@/components/API";
import SuccessNotification from "@/components/Notification/SuccessNotification";
import ErrorNotification from "@/components/Notification/ErrorNotification";
import Modal from "@/components/EditImageModal/Modal";
import Image from "next/image";
import { Country, State, City } from "country-state-city";
const indianStates = [
  { value: "Andhra Pradesh", label: "Andhra Pradesh" },
  { value: "Arunachal Pradesh", label: "Arunachal Pradesh" },
  { value: "Assam", label: "Assam" },
  { value: "Bihar", label: "Bihar" },
  { value: "Chhattisgarh", label: "Chhattisgarh" },
  { value: "Goa", label: "Goa" },
  { value: "Gujarat", label: "Gujarat" },
  { value: "Haryana", label: "Haryana" },
  { value: "Himachal Pradesh", label: "Himachal Pradesh" },
  { value: "Jharkhand", label: "Jharkhand" },
  { value: "Karnataka", label: "Karnataka" },
  { value: "Kerala", label: "Kerala" },
  { value: "Madhya Pradesh", label: "Madhya Pradesh" },
  { value: "Maharashtra", label: "Maharashtra" },
  { value: "Manipur", label: "Manipur" },
  { value: "Meghalaya", label: "Meghalaya" },
  { value: "Mizoram", label: "Mizoram" },
  { value: "Nagaland", label: "Nagaland" },
  { value: "Odisha", label: "Odisha" },
  { value: "Punjab", label: "Punjab" },
  { value: "Rajasthan", label: "Rajasthan" },
  { value: "Sikkim", label: "Sikkim" },
  { value: "Tamil Nadu", label: "Tamil Nadu" },
  { value: "Telangana", label: "Telangana" },
  { value: "Tripura", label: "Tripura" },
  { value: "Uttar Pradesh", label: "Uttar Pradesh" },
  { value: "Uttarakhand", label: "Uttarakhand" },
  { value: "West Bengal", label: "West Bengal" },
  { value: "Andaman & Nicobar", label: "Andaman & Nicobar" },
  { value: "Chandigarh", label: "Chandigarh" },
  { value: "Daman & Diu", label: "Dadra and Nagar Haveli and Daman & Diu" },
  { value: "Lakshadweep", label: "Lakshadweep" },
  { value: "Delhi", label: "Delhi" },
  { value: "Pondicherry", label: "Pondicherry" },
  { value: "Jammu & Kashmir", label: "Jammu & Kashmir" },
  { value: "Ladakh", label: "Ladakh" },
];

export default function CourseCreationOne() {
  const [successNotification, setSuccessNotification] = useState(false);
  const [errorNotification, setErrorNotification] = useState(false);
  const [message, setMessage] = useState("");
  const [showModal, setShowModal] = useState(false);
  const [profile, setProfile] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [showEdit, setShowEdit] = useState(false);
  const [isEditable, setIsEditable] = useState(true);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [loading, setLoading] = useState(false);
  const [pincodeError, setPincodeError] = useState(false);

  const [states, setStates] = useState([]);

  useEffect(() => {
    const stateData = State.getStatesOfCountry("IN");
    setStates(stateData);
  }, []);
  const phoneRegExp = /^[6-9]\d{9}$/;
  const pinCodeRegExp = /^[1-9][0-9]{5}$/;
  const emailRegExp = /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i;
  const passwordRegExp =
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[^\w\s])[A-Za-z\d!"#$%&'()*+,-./:;<=>?@[\\\]^_`{|}~]{8,}$/;

  const router = useRouter();

  const calculateAge = async (dob) => {
    const currentDate = new Date();
    const dobDate = new Date(dob);

    let age = currentDate.getFullYear() - dobDate.getFullYear();

    // Check if the current date hasn't reached the birth month and day yet
    if (
      currentDate.getMonth() < dobDate.getMonth() ||
      (currentDate.getMonth() === dobDate.getMonth() &&
        currentDate.getDate() < dobDate.getDate())
    ) {
      age--;
    }

    return age;
  };

  const deleteImageFiles = async (url) => {
    setLoading(true);
    try {
      const formData = new FormData();
      formData.append("url", url);

      const response = await axios.post(
        `${API}/api/coach/uploadImage`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );
      const resp = response?.data;
      formik.setFieldValue("profileImg", "");
      setLoading(false);
      setShowDeleteModal(!showDeleteModal);
      setProfile("");
      if (showEdit) {
        let myHeaders = new Headers();
        myHeaders.append("Content-Type", "application/json");

        let requestOptions = {
          method: "PATCH",
          headers: myHeaders,
          body: JSON.stringify({ profileImg: "" }),
        };
        let response = await fetch(`/api/coach_profile`, requestOptions);

        const result = await response.json();
        setLoading(false);
        setShowDeleteModal(!showDeleteModal);

        // console.log(result, "result of delete image");
      }
      // console.log(resp);
    } catch (error) {
      setLoading(false);
      console.log("error 106");
      setShowDeleteModal(!showDeleteModal);
    }
  };

  function formatDateToYYYYMMDD(dateString) {
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = `0${date.getMonth() + 1}`.slice(-2); // Add leading zero if needed
    const day = `0${date.getDate()}`.slice(-2); // Add leading zero if needed

    return `${year}-${month}-${day}`;
  }

  const getCoachDetails = async () => {
    try {
      let myHeaders = new Headers();
      myHeaders.append("Content-Type", "application/json");

      let requestOptions = {
        method: "GET",
        headers: myHeaders,
      };

      const response = await fetch(`/api/coach_profile`, requestOptions);

      const result = await response.json();
      setProfile(result?.profileImg);
      if (!result.error) {
        setIsEditable(false);
        formik.setValues({ ...formik.values, ...result });
        formik.setFieldValue("dob", formatDateToYYYYMMDD(result?.dob));
        result.linkedFacilities.map((x, i) => {
          formik.setFieldValue(`linkedFacilities[${i}].pinCode`, x.pincode);
        });
        setProfile(result?.profileImg);
        setShowEdit(true);
      }
    } catch (error) {
      console.log("error 145");
    }
  };

  const getDetailsFromPincode = async (pincode, index) => {
    try {
      if (!pincode.length > 0) setPincodeError(true);
      const details = await axios.get(
        `https://api.postalpincode.in/pincode/${pincode}`
      );
      // console.log(details);
      if (
        details?.data[0]?.Status == "Error" ||
        details?.data[0]?.Status == "404" ||
        details?.data[0]?.Message == "No records found"
      ) {
        // setPincodeError(true);
        // await formik.setFieldTouched(
        //   `linkedFacilities[${index}].pinCode`,
        //   "Pincode is not correct"
        // );
        // await formik.setFieldError(
        //   `linkedFacilities[${index}].pinCode`,
        //   "Pincode is not correct"
        // );
        formik.setFieldValue(`linkedFacilities[${index}].city`, "");
        formik.setFieldValue(`linkedFacilities[${index}].state`, "");
        formik.setFieldValue(`linkedFacilities[${index}].country`, "");
        return;
      } else {
        setPincodeError(false);
        formik.setFieldValue(
          `linkedFacilities[${index}].city`,
          `${details?.data[0]?.PostOffice[0]?.Name}, ${details?.data[0]?.PostOffice[0]?.District}`
        );
        formik.setFieldValue(
          `linkedFacilities[${index}].state`,
          details?.data[0]?.PostOffice[0]?.State
        );
        formik.setFieldValue(
          `linkedFacilities[${index}].country`,
          details?.data[0]?.PostOffice[0]?.Country
        );
      }
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    getCoachDetails();
  }, []);

  const formik = useFormik({
    initialValues: {
      firstName: "",
      lastName: "",
      gender: "",
      // gstState:"",
      email: "",
      mobile: "",
      dob: "",
      password: "",
      confirmPassword: "",
      profileImg: "",
      age: "",
      alternateMobile: "",
      linkedFacilities: [
        {
          name: "",
          addressLine1: "",
          addressLine2: "",
          city: "",
          state: "",
          pinCode: "",
          country: "",
          // phone: "",
          amenities: "",
        },
      ],
    },
    validationSchema: Yup.object().shape({
      firstName: Yup.string()
        .min(3, "Atleast 3 characters are required")
        .max(100, "Only 30 characters are allowed")
        .required("Name is required"),
      lastName: Yup.string()
        .min(3, "Atleast 3 characters are required")
        .max(100, "Only 30 characters are allowed")
        .required("Last name is required"),
      gender: Yup.string().required("Gender is required"),
      // gstState: Yup.string()
      // .required("GST State is required")
      // .max(100, "Only 100 characters are allowed"),
      email: Yup.string()
        .email("Invalid email")
        .matches(emailRegExp, "Invalid email")
        .required("Email is required"),
      dob: Yup.date()
        .required("Date of birth is required")
        .max(new Date(), "Date of birth cannot be in the future")
        .test("is-adult", "Must be at least 18 years old", function (value) {
          const today = new Date();
          const minAgeDate = new Date(
            today.getFullYear() - 18,
            today.getMonth(),
            today.getDate()
          );
          return value <= minAgeDate;
        }),
      password: !showEdit
        ? Yup.string()
            .matches(
              passwordRegExp,
              "Password must contain minimum eight characters, at least one uppercase letter, one lowercase letter, one number, and one special character."
            )
            .required("Password is required")
            .max(30, "Only 30 characters are allowed")
        : Yup.string(),
      confirmPassword: !showEdit
        ? Yup.string()
            .required("Password is required")
            .oneOf([Yup.ref("password"), null], "Passwords must match")
        : Yup.string(),
      mobile: Yup.string()
        .matches(phoneRegExp, "Phone number is not valid")
        .required("Please enter your phone number"),
      alternateMobile: Yup.string().matches(
        phoneRegExp,
        "Phone number is not valid"
      ),
      // .required("Please enter your phone number"),
      linkedFacilities: Yup.array().of(
        Yup.object().shape({
          name: Yup.string()
            .required("Name is required")
            .max(100, "Only 100 characters are allowed")
            .min(3, "Minimun 3 characters are required"),
          addressLine1: Yup.string().required("Address Line 1 is required"),
          addressLine2: Yup.string(),
          city: Yup.string()
            .required("City is required")
            .max(100, "Only 100 characters are allowed"),
          state: Yup.string()
            .required("State is required")
            .max(100, "Only 100 characters are allowed"),
          pinCode: Yup.string()
            .matches(pinCodeRegExp, "PIN code is not valid")
            .required("Pincode is required"),
          country: Yup.string()
            .required("Country is required")
            .max(100, "Only 100 characters are allowed"),
          // phone: Yup.string().matches(phoneRegExp, "Phone number is not valid"),
          amenities: Yup.string(),
        })
      ),
    }),
    onSubmit: async (values) => {
      // Handle submission logic here, e.g., API calls, etc.
      console.log("Form values on submit:", values);
      console.log("Form errors:", formik.errors);
      console.log("Form touched:", formik.touched);
      console.log("Form is valid:", formik.isValid);

      // Check if form has validation errors
      if (!formik.isValid) {
        console.log("Form has validation errors, not submitting");
        setErrorNotification(true);
        setMessage("Please fill all required fields correctly");
        setTimeout(() => {
          setErrorNotification(false);
        }, 3000);
        return;
      }

      try {
        if (pincodeError) {
          setErrorNotification(true);
          setMessage("Pincode is not correct");
          setTimeout(() => {
            setErrorNotification(false);
          }, 3000);
          return;
        }
        setLoading(true);
        const age = await calculateAge(values.dob);
        await formik.setFieldValue("age", age);

        if (!profile || profile === "") {
          setErrorNotification(true);
          setMessage("Plesae add profile picture");
          setTimeout(() => {
            setErrorNotification(false);
          }, 3000);
          setLoading(false);
          return;
        } else {
          setErrorNotification(false);
          await formik.setFieldValue("profileImg", `${profile}`);
          // console.log(values, profile, "pp");

          let myHeaders = new Headers();
          myHeaders.append("Content-Type", "application/json");

          let requestOptions = {
            method: "POST",
            headers: myHeaders,
            body: JSON.stringify({ ...values, age: age }),
          };

          showEdit
            ? (requestOptions = {
                method: "PATCH",
                headers: myHeaders,
                body: JSON.stringify({ ...values, age: age }),
              })
            : (requestOptions = {
                method: "POST",
                headers: myHeaders,
                body: JSON.stringify({ ...values, age: age }),
              });

          let response;

          !showEdit
            ? (response = await fetch(`/api/signup`, requestOptions))
            : (response = await fetch(`/api/coach_profile`, requestOptions));

          const result = await response.json();

          if (!result.error) {
            setLoading(false);
            if (showEdit) {
              setSuccessNotification(true);
              setErrorNotification(false);
              setMessage("Details Saved Successfully");
              setTimeout(() => {
                setSuccessNotification(false);
                router.push("/calendar");
              }, 3000);
            } else {
              setSuccessNotification(true);
              setErrorNotification(false);
              setMessage("Details Saved Successfully");
              setTimeout(() => {
                setSuccessNotification(false);
                router.push(`/profile/professional_details#signup`);
              }, 3000);
            }
          } else {
            setLoading(false);
            if (result.error && result.error.status === 400) {
              setSuccessNotification(false);
              setErrorNotification(true);
              setMessage("Coach with same mobile or email already exists");
              // setMessage(result.error.response.data.error);
              setTimeout(() => {
                setErrorNotification(true);
              }, 3000);
            }
          }
        }
      } catch (error) {
        console.log("error 391");
        setLoading(false);
        setMessage("Please try again");
        setSuccessNotification(false);
        setErrorNotification(true);
        setTimeout(() => {
          setErrorNotification(true);
        }, 3000);
        console.log("error 399");
      }
    },
  });

  const addAddress = () => {
    formik.setValues({
      ...formik.values,
      linkedFacilities: [
        ...formik.values.linkedFacilities,
        {
          name: "",
          addressLine1: "",
          addressLine2: "",
          city: "",
          state: "",
          pinCode: "",
          country: "",
          // phone: "",
          amenities: "",
        },
      ],
    });
    // setIsFacilities(true);
  };
  return (
    <>
      <title>
        {!showEdit
          ? "Coach Signup - Basic details"
          : "Coach Profile - Basic details"}
      </title>
      <DeleteDialog
        showDeleteModal={showDeleteModal}
        setShowDeleteModal={setShowDeleteModal}
        deleteImageFiles={deleteImageFiles}
        profile={profile}
        loading={loading}
        setLoading={setLoading}
      />

      {successNotification && <SuccessNotification message={message} />}

      {errorNotification && <ErrorNotification message={message} />}

      {showModal && (
        <Modal
          setProfile={setProfile}
          setFieldValue={formik.setFieldValue}
          setShowModal={setShowModal}
          showModal={showModal}
          setErrorNotification={setErrorNotification}
          setMessage={setMessage}
          showEdit={showEdit}
          setShowEdit={setShowEdit}
        />
      )}

      <form onSubmit={formik.handleSubmit}>
        {/* <fieldset disabled={!isEditable}> */}
        <div className="overflow-hidden bg-white shadow sm:rounded-lg">
          {/* <h1 className="flex justify-center"> Basic Details</h1> */}
          <div className="border-t border-gray-100">
            <dl className="divide-y divide-gray-100">
              <div className="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 ">
                <dt className="text-base font-semibold tracking-wide  text-gray-900 required">
                  Profile Image
                </dt>
                <dd className="mt-1 leading-6 text-gray-700 sm:col-span-2 sm:mt-0 flex justify-center md:justify-normal gap-x-3">
                  <div className="w-[40%]">
                    <img
                      className="relative inline-block w-[200px] h-[150px] md:h-[200px] object-cover rounded-full object-center"
                      // src={preview}
                      src={
                        profile && profile !== ""
                          ? profile
                          : "https://media.istockphoto.com/id/1300845620/vector/user-icon-flat-isolated-on-white-background-user-symbol-vector-illustration.jpg?s=612x612&w=0&k=20&c=yBeyba0hUkh14_jgv1OKqIH0CCSWU_4ckRkAoy2p73o="
                      }
                      alt="Profile Image"
                    />
                  </div>
                  <div className="flex gap-x-2 items-end">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        e.preventDefault();
                        setShowModal(true);
                      }}
                    >
                      <PencilSquareIcon
                        className="h-7 w-7"
                        aria-hidden="true"
                      />
                    </button>

                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        e.preventDefault();
                        if (profile) {
                          setShowDeleteModal(true);
                        }
                      }}
                      disabled={!profile} // Disable button if profile is empty
                      className={`${
                        !profile
                          ? "cursor-not-allowed opacity-50" // Style for the disabled state
                          : ""
                      }`}
                    >
                      <TrashIcon className="h-7 w-7" aria-hidden="true" />
                    </button>
                  </div>
                </dd>
              </div>
              <div className="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt className="text-base font-semibold tracking-wide text-gray-900">
                  Personal Information
                </dt>
                <dd className="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                  <div className="grid grid-cols-1 gap-x-8 gap-y-8  md:grid-cols-1">
                    <div>
                      <div className="grid max-w-2xl grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
                        <div className="sm:col-span-3">
                          <label
                            htmlFor="firstName"
                            className="block text-[16px ]font-medium leading-6 text-gray-900 capitalize required"
                          >
                            First name
                          </label>
                          <div className="mt-2">
                            <input
                              type="text"
                              name="firstName"
                              id="firstName"
                              {...formik.getFieldProps("firstName")}
                              autoComplete="given-name"
                              className="px-3 block w-full rounded-md border-0 py-1.5 text-gray-900  shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                            />
                            {formik.touched.firstName &&
                              formik.errors.firstName && (
                                <div className="text-red-500">
                                  {formik.errors.firstName}
                                </div>
                              )}
                          </div>
                        </div>

                        <div className="sm:col-span-3">
                          <label
                            htmlFor="lastName"
                            className="block text-[16px ]font-medium leading-6 text-gray-900 capitalize required"
                          >
                            Last name
                          </label>
                          <div className="mt-2">
                            <input
                              type="text"
                              name="lastName"
                              id="lastName"
                              autoComplete="family-name"
                              {...formik.getFieldProps("lastName")}
                              className="px-3 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                            />
                            {formik.touched.lastName &&
                              formik.errors.lastName && (
                                <div className="text-red-500">
                                  {formik.errors.lastName}
                                </div>
                              )}
                          </div>
                        </div>

                        <div className="sm:col-span-3">
                          <label
                            htmlFor="mobile"
                            className="block text-[16px ]font-medium leading-6 text-gray-900 capitalize required"
                          >
                            Mobile
                          </label>
                          <div className="mt-2 flex items-center ">
                            <span className="px-3 py-1.5 border border-gray-300 bg-gray-100 rounded-l-md text-gray-500">
                              +91
                            </span>
                            <input
                              type="number"
                              name="mobile"
                              disabled={!isEditable}
                              id="mobile"
                              onFocus={(e) =>
                                e.target.addEventListener(
                                  "wheel",
                                  function (e) {
                                    e.preventDefault();
                                  },
                                  { passive: false }
                                )
                              }
                              autoComplete="given-name"
                              {...formik.getFieldProps("mobile")}
                              className="px-3 block w-full rounded-r-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                            />
                            <style jsx>{`
                              input[type="number"]::-webkit-inner-spin-button,
                              input[type="number"]::-webkit-outer-spin-button {
                                -webkit-appearance: none;
                                margin: 0;
                              }

                              input[type="number"] {
                                -moz-appearance: textfield;
                              }
                            `}</style>
                            {formik.touched.mobile && formik.errors.mobile && (
                              <div className="text-red-500">
                                {formik.errors.mobile}
                              </div>
                            )}
                          </div>
                        </div>

                        <div className="sm:col-span-3">
                          <label
                            htmlFor="alternateMobile"
                            className="block text-[16px ]font-medium leading-6 text-gray-900 capitalize"
                          >
                            Alternate Mobile Number
                          </label>
                          <div className="mt-2 flex items-center">
                            <span className="px-3 py-1.5 border border-gray-300 bg-gray-100 rounded-l-md text-gray-500">
                              +91
                            </span>
                            <input
                              type="number"
                              name="alternateMobile"
                              onFocus={(e) =>
                                e.target.addEventListener(
                                  "wheel",
                                  function (e) {
                                    e.preventDefault();
                                  },
                                  { passive: false }
                                )
                              }
                              id="alternateMobile"
                              autoComplete="given-name"
                              {...formik.getFieldProps("alternateMobile")}
                              className="px-3 block w-full rounded-r-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                            />
                            <style jsx>{`
                              input[type="number"]::-webkit-inner-spin-button,
                              input[type="number"]::-webkit-outer-spin-button {
                                -webkit-appearance: none;
                                margin: 0;
                              }

                              input[type="number"] {
                                -moz-appearance: textfield;
                              }
                            `}</style>
                            {formik.touched.alternateMobile &&
                              formik.errors.alternateMobile && (
                                <div className="text-red-500">
                                  {formik.errors.alternateMobile}
                                </div>
                              )}
                          </div>
                        </div>

                        <div className="sm:col-span-3">
                          <label
                            htmlFor="email"
                            className="block text-[16px ]font-medium leading-6 text-gray-900 capitalize required"
                          >
                            Email address
                          </label>
                          <div className="mt-2">
                            <input
                              id="email"
                              name="email"
                              disabled={!isEditable}
                              type="email"
                              autoComplete="email"
                              {...formik.getFieldProps("email")}
                              className="px-3 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                            />
                            {formik.touched.email && formik.errors.email && (
                              <div className="text-red-500">
                                {formik.errors.email}
                              </div>
                            )}
                          </div>
                        </div>

                        <div className="sm:col-span-3">
                          <label
                            htmlFor="dob"
                            className="block text-[16px ]font-medium leading-6 text-gray-900 capitalize required"
                          >
                            Date of birth
                          </label>
                          <div className="mt-2">
                            <input
                              id="dob"
                              name="dob"
                              type="date"
                              max={formatDateToYYYYMMDD(new Date())}
                              autoComplete="off"
                              {...formik.getFieldProps("dob")}
                              className="px-3 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                            />
                            {formik.touched.dob && formik.errors.dob && (
                              <div className="text-red-500">
                                {formik.errors.dob}
                              </div>
                            )}
                          </div>
                        </div>

                        {!showEdit && (
                          <>
                            <div className="sm:col-span-3">
                              <label
                                htmlFor="password"
                                className="block text-[16px ]font-medium leading-6 text-gray-900 capitalize required"
                              >
                                Password
                              </label>
                              <div className="mt-2">
                                <div className="flex flex-row">
                                  <input
                                    id="password"
                                    name="password"
                                    type={showPassword ? "text" : "password"}
                                    autoComplete="off"
                                    {...formik.getFieldProps("password")}
                                    className="px-3 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                  />
                                  <Image
                                    height={15}
                                    width={15}
                                    style={{ marginLeft: "-30px" }}
                                    src={
                                      showPassword
                                        ? "/showEye.svg"
                                        : "/hideEye.svg"
                                    }
                                    alt=""
                                    onClick={() => {
                                      // console.log("oooo");
                                      setShowPassword(!showPassword);
                                    }}
                                  />
                                </div>
                                {formik.touched.password &&
                                  formik.errors.password && (
                                    <div className="text-red-500">
                                      {formik.errors.password}
                                    </div>
                                  )}
                              </div>
                            </div>

                            <div className="sm:col-span-3">
                              <label
                                htmlFor="confirmPassword"
                                className="block text-[16px ]font-medium leading-6 text-gray-900 capitalize required"
                              >
                                Confirm Password
                              </label>
                              <div className="mt-2">
                                <div className="flex flex-row">
                                  <input
                                    id="confirmPassword"
                                    name="confirmPassword"
                                    type={
                                      showConfirmPassword ? "text" : "password"
                                    }
                                    autoComplete="off"
                                    {...formik.getFieldProps("confirmPassword")}
                                    className="px-3 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                  />
                                  <Image
                                    height={15}
                                    width={15}
                                    style={{ marginLeft: "-30px" }}
                                    src={
                                      showConfirmPassword
                                        ? "/showEye.svg"
                                        : "/hideEye.svg"
                                    }
                                    alt=""
                                    onClick={() => {
                                      // console.log("oooo");
                                      setShowConfirmPassword(
                                        !showConfirmPassword
                                      );
                                    }}
                                  />
                                </div>
                                {formik.touched.confirmPassword &&
                                  formik.errors.confirmPassword && (
                                    <div className="text-red-500">
                                      {formik.errors.confirmPassword}
                                    </div>
                                  )}
                              </div>
                            </div>
                          </>
                        )}

                        <div className="sm:col-span-3">
                          <label
                            htmlFor="gender "
                            className="block text-[16px] font-medium leading-6 text-gray-900 capitalize required"
                          >
                            Gender
                          </label>
                          <div className="mt-2">
                            <select
                              type="text"
                              name="gender"
                              id="gender"
                              autoComplete="family-name"
                              {...formik.getFieldProps("gender")}
                              className="px-3 block w-full rounded-md border-0 py-1.5 h-9 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                            >
                              <option value="">Select Gender</option>
                              <option value="male">Male</option>
                              <option value="female">Female</option>
                              <option value="other">Other</option>
                            </select>
                            {formik.touched.gender && formik.errors.gender && (
                              <div className="text-red-500">
                                {formik.errors.gender}
                              </div>
                            )}
                          </div>
                        </div>

                        {/* state dropDown start */}

                        {/* <div className="sm:col-span-3">
                          <label
                            htmlFor="gstState"
                            className="block text-[16px] font-medium leading-6 text-gray-900 capitalize required"
                          >
                            Your Home state
                          </label>
                          <div className="mt-2">
                            <select
                              name="gstState"
                              disabled={!isEditable}
                              id="gstState"
                              autoComplete="address-level1"
                              {...formik.getFieldProps("gstState")}
                              className="px-3 block w-full rounded-md border-0 py-1.5 h-9 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                            >
                              <option value="">Select State</option>
                              {states.map((state) => (
                                <option key={state.isoCode} value={state.isoCode}>
                                  {state.name}
                                </option>
                              ))}
                            </select>
                            {formik.touched.gstState && formik.errors.gstState && (
                              <div className="text-red-500">{formik.errors.gstState}</div>
                            )}
                          </div>
                        </div> */}

                        {/* state dropDown end */}
                      </div>
                    </div>
                  </div>
                </dd>
              </div>
              <div className="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt className="text-base font-semibold tracking-wide text-gray-900">
                  Address
                </dt>
                <dd className="mt-2 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                  <div>
                    {formik.values.linkedFacilities.map((_, index) => (
                      <Fragment key={index}>
                        <div
                        // className="px-4 py-4 grid grid-cols-1 gap-x-8 gap-y-8  md:grid-cols-1 bg-white shadow-sm ring-1 ring-gray-900/5  mb-3"
                        >
                          <div
                            style={{
                              display: "flex",
                              flexDirection: "row",
                              justifyContent: "space-between",
                              borderBottom: "1px solid",
                            }}
                          >
                            <p>Address{index + 1}</p>
                            {index + 1 > 1 ? (
                              <button
                                type="button"
                                className="rounded-full bg-red-600 p-1.5 text-white shadow-sm hover:bg-red-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
                                onClick={() =>
                                  formik.setValues((prevState) => ({
                                    ...prevState,
                                    linkedFacilities:
                                      prevState.linkedFacilities.filter(
                                        (_, i) => i !== index
                                      ),
                                  }))
                                }
                              >
                                <XMarkIcon
                                  className="h-5 w-5"
                                  aria-hidden="true"
                                />
                              </button>
                            ) : null}
                          </div>
                          <div>
                            {/* Add address */}
                            <div className="grid max-w-2xl grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6 mt-3">
                              <div className="sm:col-span-3">
                                <label
                                  htmlFor={`linkedFacilities.${index}.name`}
                                  className="block text-[16px ]font-medium leading-6 text-gray-900 capitalize required"
                                >
                                  Facility Name
                                </label>
                                <div className="mt-2">
                                  <input
                                    type="text"
                                    name={`linkedFacilities.${index}.name`}
                                    id={`linkedFacilities.${index}.name`}
                                    autoComplete="given-name"
                                    {...formik.getFieldProps(
                                      `linkedFacilities.${index}.name`
                                    )}
                                    className="px-3 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                  />
                                  {formik.touched.linkedFacilities?.[index]
                                    ?.name &&
                                    formik.errors.linkedFacilities?.[index]
                                      ?.name && (
                                      <div className="text-red-500">
                                        {
                                          formik.errors.linkedFacilities?.[
                                            index
                                          ]?.name
                                        }
                                      </div>
                                    )}
                                </div>
                              </div>

                              <div className="sm:col-span-3">
                                <label
                                  htmlFor={`linkedFacilities.${index}.pincode`}
                                  className="block text-[16px ]font-medium leading-6 text-gray-900 capitalize required"
                                >
                                  Pincode
                                </label>
                                <div className="mt-2">
                                  <input
                                    name={`linkedFacilities.${index}.pinCode`}
                                    id={`linkedFacilities.${index}.pinCode`}
                                    type="number"
                                    autoComplete="family-name"
                                    onChange={(e) => {
                                      // Update formik field first
                                      formik.setFieldValue(
                                        `linkedFacilities[${index}].pinCode`,
                                        e.target.value
                                      );
                                      // Then get details from pincode
                                      getDetailsFromPincode(
                                        e.target.value,
                                        index
                                      );
                                    }}
                                    onFocus={(e) =>
                                      e.target.addEventListener(
                                        "wheel",
                                        function (e) {
                                          e.preventDefault();
                                        },
                                        { passive: false }
                                      )
                                    }
                                    {...formik.getFieldProps(
                                      `linkedFacilities.${index}.pinCode`
                                    )}
                                    className="px-3 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                  />
                                  <style jsx>{`
                                    input[type="number"]::-webkit-inner-spin-button,
                                    input[type="number"]::-webkit-outer-spin-button {
                                      -webkit-appearance: none;
                                      margin: 0;
                                    }

                                    input[type="number"] {
                                      -moz-appearance: textfield;
                                    }
                                  `}</style>
                                  {((formik.touched.linkedFacilities?.[index]
                                    ?.pinCode &&
                                    formik.errors.linkedFacilities?.[index]
                                      ?.pinCode) ||
                                    pincodeError) && (
                                    <div className="text-red-500">
                                      {
                                        formik.errors.linkedFacilities?.[index]
                                          ?.pinCode
                                      }
                                    </div>
                                  )}
                                </div>
                              </div>

                              <div className="sm:col-span-3">
                                <label
                                  htmlFor={`linkedFacilities.${index}.addressLine1`}
                                  className="block text-[16px ]font-medium leading-6 text-gray-900 capitalize required"
                                >
                                  Address Line 1
                                </label>
                                <div className="mt-2">
                                  <input
                                    type="text"
                                    name={`linkedFacilities.${index}.addressLin1`}
                                    id={`linkedFacilities.${index}.addressLine1`}
                                    autoComplete="given-name"
                                    {...formik.getFieldProps(
                                      `linkedFacilities.${index}.addressLine1`
                                    )}
                                    className="px-3 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                  />
                                  {formik.touched.linkedFacilities?.[index]
                                    ?.addressLine1 &&
                                    formik.errors.linkedFacilities?.[index]
                                      ?.addressLine1 && (
                                      <div className="text-red-500">
                                        {
                                          formik.errors.linkedFacilities?.[
                                            index
                                          ]?.addressLine1
                                        }
                                      </div>
                                    )}
                                </div>
                              </div>

                              <div className="sm:col-span-3">
                                <label
                                  htmlFor={`linkedFacilities.${index}.addressLine2`}
                                  className="block text-[16px ]font-medium leading-6 text-gray-900 capitalize"
                                >
                                  Address Line 2
                                </label>
                                <div className="mt-2">
                                  <input
                                    type="text"
                                    name={`linkedFacilities.${index}.addressLine2`}
                                    id={`linkedFacilities.${index}.addressLine2`}
                                    autoComplete="family-name"
                                    {...formik.getFieldProps(
                                      `linkedFacilities.${index}.addressLine2`
                                    )}
                                    className="px-3 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                  />
                                  {formik.touched.linkedFacilities?.[index]
                                    ?.addressLine2 &&
                                    formik.errors.linkedFacilities?.[index]
                                      ?.addressLine2 && (
                                      <div className="text-red-500">
                                        {
                                          formik.errors.linkedFacilities?.[
                                            index
                                          ]?.addressLine2
                                        }
                                      </div>
                                    )}
                                </div>
                              </div>

                              <div className="sm:col-span-2">
                                <label
                                  htmlFor={`linkedFacilities.${index}.city`}
                                  className="block text-[16px ]font-medium leading-6 text-gray-900 capitalize required"
                                >
                                  City
                                </label>
                                <div className="mt-2">
                                  <input
                                    name={`linkedFacilities.${index}.city`}
                                    id={`linkedFacilities.${index}.city`}
                                    type="text"
                                    autoComplete="family-name"
                                    {...formik.getFieldProps(
                                      `linkedFacilities.${index}.city`
                                    )}
                                    className="px-3 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                  />
                                  {formik.touched.linkedFacilities?.[index]
                                    ?.city &&
                                    formik.errors.linkedFacilities?.[index]
                                      ?.city && (
                                      <div className="text-red-500">
                                        {
                                          formik.errors.linkedFacilities?.[
                                            index
                                          ]?.city
                                        }
                                      </div>
                                    )}
                                </div>
                              </div>

                              {/* <div className="sm:col-span-2">
                                <label
                                  htmlFor={`linkedFacilities.${index}.state`}
                                  className="block text-[16px ]font-medium leading-6 text-gray-900 capitalize required"
                                >
                                  State
                                </label>
                                <div className="mt-2">
                                  <input
                                    name={`linkedFacilities.${index}.state`}
                                    id={`linkedFacilities.${index}.state`}
                                    type="text"

                                    autoComplete="family-name"
                                    {...formik.getFieldProps(
                                      `linkedFacilities.${index}.state`
                                    )}
                                    className="px-3 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                  />
                                  {formik.touched.linkedFacilities?.[index]
                                    ?.state &&
                                    formik.errors.linkedFacilities?.[index]
                                      ?.state && (
                                      <div className="text-red-500">
                                        {
                                          formik.errors.linkedFacilities?.[
                                            index
                                          ]?.state
                                        }
                                      </div>
                                    )}
                                </div>
                              </div> */}

                              <div className="sm:col-span-2">
                                <label
                                  htmlFor={`linkedFacilities.${index}.state`}
                                  className="block text-[16px] font-medium leading-6 text-gray-900 capitalize required"
                                >
                                  State
                                </label>
                                <div className="mt-2">
                                  <Select
                                    id={`linkedFacilities.${index}.state`}
                                    name={`linkedFacilities.${index}.state`}
                                    options={indianStates}
                                    value={indianStates.find(
                                      (option) =>
                                        option.value ===
                                        formik.values.linkedFacilities?.[index]
                                          ?.state
                                    )}
                                    onChange={(selectedOption) =>
                                      formik.setFieldValue(
                                        `linkedFacilities.${index}.state`,
                                        selectedOption.value
                                      )
                                    }
                                    onBlur={() =>
                                      formik.setFieldTouched(
                                        `linkedFacilities.${index}.state`,
                                        true
                                      )
                                    }
                                    className="basic-single"
                                    classNamePrefix="select"
                                  />
                                  {formik.touched.linkedFacilities?.[index]
                                    ?.state &&
                                    formik.errors.linkedFacilities?.[index]
                                      ?.state && (
                                      <div className="text-red-500">
                                        {
                                          formik.errors.linkedFacilities?.[
                                            index
                                          ]?.state
                                        }
                                      </div>
                                    )}
                                </div>
                              </div>
                              <div className="sm:col-span-2">
                                <label
                                  htmlFor={`linkedFacilities.${index}.country`}
                                  className="block text-[16px ]font-medium leading-6 text-gray-900 capitalize required"
                                >
                                  Country
                                </label>
                                <div className="mt-2">
                                  <input
                                    type="text"
                                    name={`linkedFacilities.${index}.country`}
                                    id={`linkedFacilities.${index}.country`}
                                    autoComplete="family-name"
                                    {...formik.getFieldProps(
                                      `linkedFacilities.${index}.country`
                                    )}
                                    className="px-3 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                  />
                                  {formik.touched.linkedFacilities?.[index]
                                    ?.country &&
                                    formik.errors.linkedFacilities?.[index]
                                      ?.country && (
                                      <div className="text-red-500">
                                        {
                                          formik.errors.linkedFacilities?.[
                                            index
                                          ]?.country
                                        }
                                      </div>
                                    )}
                                </div>
                              </div>

                              <div className="sm:col-span-6">
                                <label
                                  htmlFor={`linkedFacilities.${index}.amenities`}
                                  className="block text-[16px ]font-medium leading-6 text-gray-900 capitalize"
                                >
                                  Amenities
                                </label>
                                <div className="mt-2">
                                  <ReactQuill
                                    className="border rounded"
                                    value={
                                      formik.values.linkedFacilities[index]
                                        .amenities
                                    }
                                    onChange={(value) => {
                                      formik.setFieldValue(
                                        `linkedFacilities.${index}.amenities`,
                                        value
                                      );
                                    }}
                                  />
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <br />
                      </Fragment>
                    ))}
                  </div>

                  <div className="mt-4">
                    <button
                      type="button"
                      className="rounded-md bg-white px-8 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50"
                      onClick={addAddress}
                    >
                      Add
                    </button>
                  </div>
                </dd>
              </div>
              <div className="mt-3 flex items-center justify-end gap-x-6 p-4">
                <button
                  type="button"
                  onClick={() => {
                    showEdit ? router.push("/calendar") : router.push("/login");
                  }}
                  className="text-sm font-semibold leading-6 text-gray-900"
                >
                  Cancel
                </button>
                {loading ? (
                  <button
                    disabled
                    type="button"
                    className="text-white px-3 py-2.5 bg-indigo-700 hover:bg-blue-800   font-medium rounded text-sm text-center mr-2 dark:bg-indigo-600 dark:hover:bg-indigo-700  inline-flex items-center"
                  >
                    <svg
                      aria-hidden="true"
                      role="status"
                      class="inline mr-3 w-4 h-4 text-white animate-spin"
                      viewBox="0 0 100 101"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                        fill="#E5E7EB"
                      ></path>
                      <path
                        d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                        fill="currentColor"
                      ></path>
                    </svg>
                    Loading...
                  </button>
                ) : (
                  <>
                    <button
                      type="button"
                      onClick={() => {
                        console.log("Manual validation check:");
                        console.log("Form values:", formik.values);
                        console.log("Form errors:", formik.errors);
                        console.log("Form touched:", formik.touched);
                        console.log("Form is valid:", formik.isValid);
                        formik.validateForm().then((errors) => {
                          console.log("Validation errors:", errors);
                        });
                      }}
                      className="rounded-md bg-gray-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-gray-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-gray-600 mr-2"
                    >
                      Debug Form
                    </button>
                    <button
                      type="submit"
                      // disabled={formik.dirty}
                      className="rounded-md bg-sky-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-sky-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-sky-600"
                    >
                      Save
                    </button>
                  </>
                )}
              </div>
            </dl>
          </div>
        </div>
        {/* </fieldset> */}
      </form>
    </>
  );
}

function DeleteDialog({
  showDeleteModal,
  setShowDeleteModal,
  deleteImageFiles,
  profile,
  loading,
}) {
  return (
    <>
      <Transition.Root show={showDeleteModal} as={Fragment}>
        <Dialog
          as="div"
          className="relative z-10"
          onClose={() => {
            setShowDeleteModal(false);
          }}
        >
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
          </Transition.Child>

          <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
            <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                enterTo="opacity-100 translate-y-0 sm:scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 translate-y-0 sm:scale-100"
                leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              >
                <Dialog.Panel className="relative transform overflow-hidden rounded-lg mb-[70%] bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-sm sm:p-6">
                  <div>
                    {/* <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
                    <CheckIcon
                      className="h-6 w-6 text-green-600"
                      aria-hidden="true"
                    />
                  </div> */}
                    <div className="mt-3 text-center sm:mt-5">
                      <Dialog.Title
                        as="h3"
                        className="text-base font-semibold leading-6 text-gray-900"
                      >
                        {"Delete Image ?"}
                      </Dialog.Title>
                      <div className="mt-2">
                        <p className="text-sm text-gray-500">
                          Do you really want to delete the profile image?
                        </p>
                      </div>
                    </div>
                  </div>
                  <div className="mt-5 sm:mt-6 flex justify-end gap-7">
                    <button
                      type="button"
                      className="inline-flex justify-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
                      onClick={() => setShowDeleteModal(false)}
                    >
                      Cancel
                    </button>
                    {loading ? (
                      <button
                        disabled
                        type="button"
                        className="text-white px-3 py-2.5 bg-red-500 hover:bg-red-500 font-medium rounded text-sm text-center mr-2 dark:bg-red-600 dark:hover:bg-red-500 inline-flex items-center"
                      >
                        <svg
                          aria-hidden="true"
                          role="status"
                          class="inline mr-3 w-4 h-4 text-white animate-spin"
                          viewBox="0 0 100 101"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                            fill="#E5E7EB"
                          ></path>
                          <path
                            d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                            fill="currentColor"
                          ></path>
                        </svg>
                        Loading...
                      </button>
                    ) : (
                      <button
                        type="button"
                        className="inline-flex justify-center rounded-md bg-red-500 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-red-600 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-red-600"
                        onClick={() => deleteImageFiles(profile)}
                      >
                        Delete
                      </button>
                    )}
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition.Root>
    </>
  );
}
