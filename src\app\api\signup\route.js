import API from "@/components/API";
import { verifyToken } from "@/helpers/getDataFromToken";
import axios from "axios";
import { NextResponse } from "next/server";
import { getGeoLocations } from "@/helpers/getGeoLocation";

export async function POST(req, res) {
  try {
    let reqBody = await req.json();
    if (
      reqBody &&
      reqBody.linkedFacilities &&
      reqBody.linkedFacilities.length > 0
    ) {
      for (let i = 0; i < reqBody.linkedFacilities.length; i++) {
        let x = reqBody.linkedFacilities[i];
        let data = await getGeoLocations(
          `${x.name} ${x.addressLine1} ${x.city}`
        );
        if (!x.location) {
          x.location = {};
        }
        x.location.coordinates = [data.data.Latitude, data.data.Longitude];
      }
    }

    const response = await axios.post(`${API}/api/coach`, reqBody, {
      headers: { "Content-Type": "application/json" },
    });
    // console.log(response, "10202");
    const token = response?.data?.token;

    const resp = NextResponse.json({
      message: "Signup Successfull",
      success: true,
    });
    // console.log(response, "response");
    resp.cookies.set("coach-token", token, { httpOnly: true, maxAge: 86400 });
    // resp.cookies.set("tokenClient", token, { httpOnly: false });

    const payload = await verifyToken(token);
    resp.cookies.set("id", payload.id, { httpOnly: true });
    return resp;
  } catch (error) {
    // console.error("error", error);
    return new NextResponse(JSON.stringify({ error: error }));
  }
}
