"use client";

import { useState, useEffect } from "react";
import { useFormik } from "formik";
import * as Yup from "yup";
import API from "@/components/API";
import axios from "axios";
import { PencilSquareIcon } from "@heroicons/react/20/solid";
import SuccessNotification from "@/components/Notification/SuccessNotification";
import ErrorNotification from "@/components/Notification/ErrorNotification";
import { useRouter } from "next/navigation";
import VerificationBanner from "@/components/verificationBanner/VerificationBanner";
import { Country, State, City } from 'country-state-city';
import ConfirmaitonModal from "@/components/ConfirmationModal/ConfirmationModal";
export default function CourseCreationThree() {
  const [selectedImages, setSelectedImages] = useState([]);

  const [isEditable, setIsEditable] = useState(true);
  const [showEdit, setShowEdit] = useState(false);
  const [verificationModal, setVerificationModal] = useState(false);

  const [successNotification, setSuccessNotification] = useState(false);
  const [errorNotification, setErrorNotification] = useState(false);
  const [message, setMessage] = useState("");

  const [states, setStates] = useState([]);

  const [showConfirmation, setShowConfirmation] = useState(false);
  const [open, setOpen] = useState(false);
  const [saveData, setSaveData] = useState();
  const router = useRouter();

  const panRegex = /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/;
  const gstRegex = /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/;
 useEffect(() => {
    const stateData = State.getStatesOfCountry("IN");
    setStates(stateData);
  }, []);
  const updateDB = async ({ images }) => {
    try {
      let myHeaders = new Headers();
      myHeaders.append("Content-Type", "application/json");

      let requestOptions = {
        method: "PATCH",
        headers: myHeaders,
        body: JSON.stringify({ kycDocuments: { documentImg: [...images] } }),
      };
      let response = await fetch(`/api/coach_profile`, requestOptions);

      const result = await response.json();

      // console.log(result, "result of delete image");
    } catch (error) {
      console.log("error 45");
    }
  };

  const handleFileChange = async (e, index) => {
    try {
      const file = e.currentTarget.files[0];

      if (file && file.size > 10 * 1024 * 1024) {
        setErrorNotification(true);
        setMessage("Please select a file less than 10 MB.");
        setTimeout(() => {
          setErrorNotification(false);
        }, 3000);
        return;
      }

      const formData = new FormData();
      formData.append("image", file);

      const response = await axios.post(
        `${API}/api/coach/uploadImage`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );

      const url = response?.data?.url;

      await setSelectedImages([...selectedImages, { url: url }]);
      await updateDB({ images: [...selectedImages, { url: url }] });

      formik.setFieldValue(`kycDocuments.documentImg.${index}.url`, url);
    } catch (error) {
      console.log("error 82");
    }
  };

  const deleteImageFiles = async (url, index) => {
    try {
      const formData = new FormData();
      formData.append("url", url);

      const response = await axios.post(
        `${API}/api/coach/uploadImage`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );

      const resp = await response?.data;

      const updatedImages = selectedImages.filter((x, idx) => idx !== index);

      await updateDB({
        images: updatedImages,
      });

      setSelectedImages(selectedImages.filter((x, idx) => idx !== index));
    } catch (error) {
      console.log("error 111");
    }
  };

  const getCoachDetails = async () => {
    try {
      let myHeaders = new Headers();
      myHeaders.append("Content-Type", "application/json");

      let requestOptions = {
        method: "GET",
        headers: myHeaders,
      };

      const response = await fetch(`/api/coach_profile`, requestOptions);

      const result = await response.json();
      if (!result.error) {
        formik.setValues({ ...formik.values, ...result });
        setShowEdit(true);
        let tempImages = [];
        await Promise.all(
          result.kycDocuments.documentImg.map(async (x) => {
            x.url &&
              x.url !== "" &&
              tempImages.push({
                url: await x.url,
              });
          })
        );
        setSelectedImages([...selectedImages, ...tempImages]);
        if (
          result.status === "inactive" &&
          result.kycDocuments.documentImg.length > 0
        ) {
          setVerificationModal(true);
          setMessage(
            "Your KYC details have been successfully submitted. Upon approval by the administrator, you will be able to create courses"
          );
        }
        if (
          result.status !== "active" &&
          result.kycDocuments.documentImg.length == 0
        ) {
          setSuccessNotification(true);
          setMessage(
            "Your Profile is authorized. Please add KYC details to proceed."
          );
          setTimeout(() => {
            setSuccessNotification(false);
          }, 5000);
        } else {
          if (!result.kycDocuments.documentImg.length > 0) {
            setVerificationModal(true);
            setMessage(
              "Your details have been saved successfully. Your application is currently being processed, and we will notify you once it has been approved. After approval, you will be able to create courses"
            );
          }
        }
        if (
          !result.kycDocuments.documentNumber ||
          result.kycDocuments.documentNumber === "" ||
          result.kycDocuments.documentImg.length == 0
        ) {
          setIsEditable(true);
        } else {
          setIsEditable(false);
        }
      }
    } catch (error) {
      console.log("error 181");
    }
  };

  useEffect(() => {
    getCoachDetails();
  }, []);

  const regExp = /^[0-9]\d{99}$/;

  const formik = useFormik({
    initialValues: {
      kycDocuments: {
        documentName: "",
        documentNumber: "",
        documentImg: [],
      },
      bankDetails: {
        accountNumber: "",
        accountHolderName: "",
        ifsc: "",
      },
      hasGst: false,
      gstNumber: "",
      gstState:"",
    },
    validationSchema: Yup.object().shape({
      kycDocuments: Yup.object().shape({
        //  documentName: Yup.string().required("Document Name is required"),
        documentNumber: Yup.string()
          .matches(
            panRegex,
            "Must be a valid pan number with all upper case character"
          )
          .required("Document Number is required"),
        documentImg: Yup.array().of(
          Yup.object().shape({
            url: Yup.string().url("Invalid URL"),
          })
        ),
      }),
      bankDetails: Yup.object().shape({
        accountNumber: Yup.string()
          .max(20, "Not more than 20 characters")
          .required("Account number is required"),
        accountHolderName: Yup.string()
          .max(100, "Only 100 characters are allowed")
          .required("Account holder name is required"),
        ifsc: Yup.string()
          .max(20, "Only 30 characters are allowed")
          .required("IFSC Code is required"),
      }),
      hasGst: Yup.boolean(),
      // gstNumber: Yup.string().matches(
      //   gstRegex,
      //   "Please enter valid gst number"
      // ),
      gstState: Yup.string(),
      gstNumber: Yup.string()
        .matches(gstRegex, "Please enter a valid GST number")
        .when("hasGst", {
          is: true,
          then: (schema) => schema.required("GST Number is required"),
          otherwise: (schema) => schema.notRequired(),
        }),
    }),
    onSubmit: async (values) => {
      try {
        // console.log(values);

        if (values.kycDocuments.documentImg.length == 0) {
          setErrorNotification(true);
          setMessage("Please upload the document images");
          setTimeout(() => {
            setErrorNotification(false);
          }, 3000);
          return;
        }

        let myHeaders = new Headers();
        myHeaders.append("Content-Type", "application/json");

        let requestOptions = {
          method: "PATCH",
          headers: myHeaders,
          body: JSON.stringify(values),
        };

        const response = await fetch(`/api/coach_profile`, requestOptions);

        const result = await response.json();

        // console.log(result);

        if (!result.error) {
          setSuccessNotification(true);
          setIsEditable(false);
          if (result.status === "active") {
            setTimeout(() => {
              setSuccessNotification(false);
              router.push("/calendar");
            }, 3000);
            setMessage("Details Saved Successfully");
          } else {
            setSuccessNotification(false);
            setVerificationModal(true);
            setMessage(
              "Details Saved Successfully.Your application is under process and we will inform you once approved"
            );
          }
        } else {
          setErrorNotification(true);
          setIsEditable(true);
          setMessage("Something went wrong. Please try again later");
          setTimeout(() => {
            setErrorNotification(false);
          }, 3000);
        }
        // console.log(result, "pp");
        // router.push(`/profile/coach_creation_3/${id}`);
      } catch (error) {
        console.error(error, "oo");
      }
    },
  });
  const handleConfirmationModal = (formik) => {
    setOpen(true);
    setSaveData(formik);
  };
  return (
    <>
      <title>
        {isEditable
          ? "Coach Signup - Kyc details"
          : "Coach Profile - Kyc details"}
      </title>

      {verificationModal && (
        <VerificationBanner
          verificationModal={verificationModal}
          setVerificationModal={setVerificationModal}
          message={message}
        />
      )}

<ConfirmaitonModal open={open} setOpen={setOpen} saveData={saveData}/>


      {successNotification && <SuccessNotification message={message} />}

      {errorNotification && <ErrorNotification message={message} />}
      <form>
        <fieldset disabled={!isEditable}>
          <div className="overflow-hidden bg-white shadow sm:rounded-lg">
            <h2 className="text-red-500 text-center text-lg">
              ***Note: To update any of these fields please contact to admin.***
            </h2>

            <div className="border-t border-gray-100">
              <dl className="divide-y divide-gray-100">
                <div className="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                  <dt className="text-sm font-semibold tracking-wide text-gray-900">
                    KYC Details
                  </dt>
                  <dd className="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                    <div className="grid grid-cols-1 gap-x-8 gap-y-8  md:grid-cols-1">
                      <div>
                        <div className="grid max-w-2xl grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
                          <div className="sm:col-span-6">
                            <label
                              htmlFor="kycDocuments.documentNumber"
                              className="block text-[16px ]font-medium leading-6 text-gray-900 capitalize"
                            >
                              PAN Number
                            </label>
                            <div className="mt-2">
                              <input
                                type="text"
                                name="kycDocuments.documentNumber"
                                id="kycDocuments.documentNumber"
                                autoComplete="none"
                                onInput={(e) => {
                                  e.target.value = e.target.value.toUpperCase();
                                }}
                                placeholder="Enter PAN No."
                                className="block w-full rounded-md border-0 py-1.5 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                {...formik.getFieldProps(
                                  "kycDocuments.documentNumber"
                                )}
                              />
                              {formik.touched.kycDocuments?.documentNumber &&
                                formik.errors.kycDocuments?.documentNumber && (
                                  <div className="text-red-500">
                                    {formik.errors.kycDocuments?.documentNumber}
                                  </div>
                                )}
                            </div>
                            <div className="grid max-w-2xl mt-4 grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
                              <div className="col-span-full bg-white rounded-sm sm:col-span-2">
                                <div className="relative block w-full rounded-lg border-2 border-dashed border-gray-300 p-5 text-center hover:border-gray-400">
                                  {selectedImages.length === 0 && (
                                    <label
                                      htmlFor={`kycDocuments.documentImg.${0}.url`}
                                      className="cursor-pointer flex flex-col items-center"
                                      onClick={(e) => e.stopPropagation()}
                                    >
                                      {/* SVG icon */}
                                      <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        strokeWidth={1.5}
                                        stroke="currentColor"
                                        className="w-8 h-8"
                                      >
                                        <path
                                          strokeLinecap="round"
                                          strokeLinejoin="round"
                                          d="m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z"
                                        />
                                      </svg>

                                      <span className=" block text-xs text-gray-500">
                                        <span className="font-semibold text-blue-500">
                                          Upload a file
                                        </span>
                                        <br /> PNG, JPG, GIF up to 10MB
                                      </span>
                                      <input
                                        id={`kycDocuments.documentImg.${0}.url`}
                                        name={`kycDocuments.documentImg.${0}.url`}
                                        type="file"
                                        disabled={!isEditable}
                                        accept="image/*"
                                        onChange={(e) => handleFileChange(e, 0)}
                                        className="hidden"
                                      />
                                      {formik.touched.kycDocuments
                                        ?.documentImg?.[0]?.url &&
                                        formik.errors.kycDocuments
                                          ?.documentImg?.[0]?.url && (
                                          <div className="text-red-500">
                                            {
                                              formik.errors.kycDocuments
                                                ?.documentImg?.[0]?.url
                                            }
                                          </div>
                                        )}
                                    </label>
                                  )}
                                  <div>
                                    {selectedImages.length > 0 && (
                                      <div className="mt-1">
                                        <div className="relative">
                                          <img
                                            src={selectedImages[0]?.url}
                                            alt="Selected Image"
                                            className="w-full h-full object-cover rounded"
                                          />
                                          <button
                                            disabled={!isEditable}
                                            onClick={async (e) => {
                                              e.preventDefault();
                                              await deleteImageFiles(
                                                formik.values.kycDocuments
                                                  .documentImg[0].url,
                                                0
                                              );
                                              formik.setFieldValue(
                                                `kycDocuments.documentImg.${0}.url`,
                                                ""
                                              );
                                            }}
                                            className="absolute top-2 right-2 bg-white p-1 rounded-full hover:bg-gray-200 focus:outline-none z-10"
                                          >
                                            <svg
                                              xmlns="http://www.w3.org/2000/svg"
                                              fill="none"
                                              viewBox="0 0 24 24"
                                              stroke="currentColor"
                                              className="h-4 w-4 text-red-500"
                                            >
                                              <path
                                                strokeLinecap="round"
                                                strokeLinejoin="round"
                                                d="M6 18L18 6M6 6l12 12"
                                              />
                                            </svg>
                                          </button>
                                        </div>
                                      </div>
                                    )}
                                  </div>
                                </div>
                              </div>
                              <div className="col-span-full bg-white rounded-sm sm:col-span-2">
                                {selectedImages.length >= 1 && (
                                  <div className="relative block w-full rounded-lg border-2 border-dashed border-gray-300 p-5 text-center hover:border-gray-400">
                                    {selectedImages.length === 1 && (
                                      <label
                                        htmlFor={`kycDocuments.documentImg.${1}.url`}
                                        className="cursor-pointer flex flex-col items-center"
                                        onClick={(e) => e.stopPropagation()}
                                      >
                                        {/* SVG icon */}
                                        <svg
                                          xmlns="http://www.w3.org/2000/svg"
                                          fill="none"
                                          viewBox="0 0 24 24"
                                          strokeWidth={1.5}
                                          stroke="currentColor"
                                          className="w-8 h-8"
                                        >
                                          <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            d="m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z"
                                          />
                                        </svg>

                                        <span className=" block text-xs text-gray-500">
                                          <span className="font-semibold text-blue-500">
                                            Upload a file
                                          </span>
                                          <br /> PNG, JPG, GIF up to 10MB
                                        </span>
                                        <input
                                          id={`kycDocuments.documentImg.${1}.url`}
                                          name={`kycDocuments.documentImg.${1}.url`}
                                          type="file"
                                          disabled={!isEditable}
                                          accept="image/*"
                                          onChange={(e) =>
                                            handleFileChange(e, 1)
                                          }
                                          className="hidden"
                                        />
                                        {formik.touched.kycDocuments
                                          ?.documentImg?.[1]?.url &&
                                          formik.errors.kycDocuments
                                            ?.documentImg?.[1]?.url && (
                                            <div className="text-red-500">
                                              {
                                                formik.errors.kycDocuments
                                                  ?.documentImg?.[1]?.url
                                              }
                                            </div>
                                          )}
                                      </label>
                                    )}
                                    <div>
                                      {selectedImages.length > 1 && (
                                        <div className="mt-1">
                                          {selectedImages[1]?.url &&
                                            selectedImages[1]?.url !== "" && (
                                              <div className="relative">
                                                <img
                                                  src={selectedImages[1]?.url}
                                                  alt="Selected Image"
                                                  className="w-full h-full object-cover rounded"
                                                />
                                                <button
                                                  disabled={!isEditable}
                                                  onClick={async (e) => {
                                                    e.preventDefault();
                                                    await deleteImageFiles(
                                                      formik.values.kycDocuments
                                                        .documentImg[1].url,
                                                      1
                                                    );
                                                    formik.setFieldValue(
                                                      `kycDocuments.documentImg.${1}.url`,
                                                      ""
                                                    );
                                                  }}
                                                  className="absolute top-2 right-2 bg-white p-1 rounded-full hover:bg-gray-200 focus:outline-none z-10"
                                                >
                                                  <svg
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    fill="none"
                                                    viewBox="0 0 24 24"
                                                    stroke="currentColor"
                                                    className="h-4 w-4 text-red-500"
                                                  >
                                                    <path
                                                      strokeLinecap="round"
                                                      strokeLinejoin="round"
                                                      d="M6 18L18 6M6 6l12 12"
                                                    />
                                                  </svg>
                                                </button>
                                              </div>
                                            )}
                                        </div>
                                      )}
                                    </div>
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </dd>
                </div>
                <div className="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                  <dt className="text-sm font-semibold tracking-wide text-gray-900">
                    Account Details
                  </dt>
                  <dd className="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                    <div className="grid grid-cols-1 gap-x-8 gap-y-8  md:grid-cols-1">
                      <div>
                        <div className="grid max-w-2xl grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
                          <div className="sm:col-span-3">
                            <label
                              htmlFor="bankDetails.accountHolderName"
                              className="block text-[16px ]font-medium leading-6 text-gray-900 capitalize"
                            >
                              Account Holder Name
                            </label>
                            <div className="mt-2">
                              <input
                                type="text"
                                name="bankDetails.accountHolderName"
                                id="bankDetails.accountHolderName"
                                autoComplete="none"
                                placeholder="Enter name"
                                {...formik.getFieldProps(
                                  "bankDetails.accountHolderName"
                                )}
                                className="block w-full rounded-md border-0 py-1.5 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                              />
                              {formik.touched.bankDetails?.accountHolderName &&
                                formik.errors.bankDetails
                                  ?.accountHolderName && (
                                  <div className="text-red-500">
                                    {
                                      formik.errors.bankDetails
                                        ?.accountHolderName
                                    }
                                  </div>
                                )}
                            </div>
                          </div>
                          <div className="sm:col-span-3">
                            <label
                              htmlFor="bankDetails.accountNumber"
                              className="block text-[16px ]font-medium leading-6 text-gray-900 capitalize"
                            >
                              Account No.
                            </label>
                            <div className="mt-2">
                              <input
                                type="number"
                                name="bankDetails.accountNumber"
                                id="bankDetails.accountNumber"
                                maxLength={10}
                                max={10}
                                autoComplete="none"
                                onFocus={(e) =>
                                  e.target.addEventListener(
                                    "wheel",
                                    function (e) {
                                      e.preventDefault();
                                    },
                                    { passive: false }
                                  )
                                }
                                placeholder="Enter account no."
                                {...formik.getFieldProps(
                                  "bankDetails.accountNumber"
                                )}
                                className="block w-full rounded-md border-0 py-1.5 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                              />
                              <style jsx>{`
                                input[type="number"]::-webkit-inner-spin-button,
                                input[type="number"]::-webkit-outer-spin-button {
                                  -webkit-appearance: none;
                                  margin: 0;
                                }

                                input[type="number"] {
                                  -moz-appearance: textfield;
                                }
                              `}</style>
                              {formik.touched.bankDetails?.accountNumber &&
                                formik.errors.bankDetails?.accountNumber && (
                                  <div className="text-red-500">
                                    {formik.errors.bankDetails?.accountNumber}
                                  </div>
                                )}
                            </div>
                          </div>
                          <div className="sm:col-span-3">
                            <label
                              htmlFor="bankDetails.ifsc"
                              className="block text-[16px ]font-medium leading-6 text-gray-900 capitalize"
                            >
                              IFSC Code
                            </label>
                            <div className="mt-2">
                              <input
                                type="text"
                                name="bankDetails.ifsc"
                                id="bankDetails.ifsc"
                                autoComplete="none"
                                placeholder="Enter IFSC code"
                                {...formik.getFieldProps("bankDetails.ifsc")}
                                className="block w-full rounded-md border-0 py-1.5 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                              />
                              {formik.touched.bankDetails?.ifsc &&
                                formik.errors.bankDetails?.ifsc && (
                                  <div className="text-red-500">
                                    {formik.errors.bankDetails?.ifsc}
                                  </div>
                                )}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </dd>
                </div>

                <div className="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                  <dt className="text-sm font-semibold tracking-wide text-gray-900">
                    GST Details
                  </dt>
                  <dd className="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                    <div className="grid grid-cols-1 gap-x-8 gap-y-8  md:grid-cols-1">
                      <div>
                        <div className="grid max-w-2xl grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
                          <div className="col-span-full">
                            <div className="flex gap-4 items-center">
                              <label
                                htmlFor="hasGst"
                                className="mb-2 block text-[16px ]font-medium leading-6 text-gray-900 capitalize"
                              >
                                Has GST ?
                              </label>
                              <div className="flex items-center gap-1">
                                <input
                                  type="radio"
                                  id="yes"
                                  name="hasGst"
                                  checked={formik.values.hasGst}
                                  // disabled={!isEditable}
                                  value="yes"
                                  onChange={(e) =>
                                    formik.setFieldValue(
                                      "hasGst",
                                      e.target.value == "yes" ? true : false
                                    )
                                  }
                                />
                                <label
                                  htmlFor="yes"
                                  className="text-sm text-gray-700"
                                >
                                  Yes
                                </label>
                              </div>
                              <div className="flex items-center gap-1">
                                <input
                                  type="radio"
                                  id="no"
                                  // disabled={!isEditable}
                                  name="hasGst"
                                  checked={!formik.values.hasGst}
                                  value="no"
                                  onChange={(e) =>
                                    formik.setFieldValue(
                                      "hasGst",
                                      e.target.value == "yes" ? true : false
                                    )
                                  }
                                />
                                <label
                                  htmlFor="no"
                                  className="text-sm text-gray-700"
                                >
                                  No
                                </label>
                              </div>
                            </div>
                          </div>
                          {formik.values.hasGst && (
                            <div className="sm:col-span-3">
                              <label
                                htmlFor="gstNumber"
                                className="block text-[16px ]font-medium leading-6 text-gray-900 capitalize"
                              >
                                GST Account No.
                              </label>
                              <div className="mt-2">
                                <input
                                  type="text"
                                  name="gstNumber"
                                  id="gstNumber"
                                  autoComplete="none"
                                  placeholder="Enter GST number."
                                  {...formik.getFieldProps("gstNumber")}
                                  className="block w-full rounded-md border-0 py-1.5 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                />
                                {formik.touched.gstNumber &&
                                  formik.errors.gstNumber && (
                                    <div className="text-red-500">
                                      {formik.errors.gstNumber}
                                    </div>
                                  )}
                              </div>
                            </div>
                            
                          )}
                          {formik.values.hasGst && (
                            <div className="sm:col-span-3">
                            <label
                              htmlFor="gstState"
                              className="block text-[16px] font-medium leading-6 text-gray-900 capitalize required"
                            >
                             GST state
                            </label>
                            <div className="mt-2">
                              <select
                                name="gstState"
                                disabled={!isEditable}
                                id="gstState"
                                autoComplete="address-level1"
                                {...formik.getFieldProps("gstState")}
                                className="px-3 block w-full rounded-md border-0 py-1.5 h-9 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                              >
                                <option value="">Select State</option>
                                {states.map((state) => (
                                  <option key={state.isoCode} value={state.isoCode}>
                                    {state.name}
                                  </option>
                                ))}
                              </select>
                              {formik.touched.gstState && formik.errors.gstState && (
                                <div className="text-red-500">{formik.errors.gstState}</div>
                              )}
                            </div>
                          </div> 
                          )}
                        </div>
                      </div>
                    </div>
                  </dd>
                </div>
                {isEditable && (
                  <div className="mt-3 flex items-center justify-end gap-x-6 p-4">
                    <button
                      type="button"
                      className="text-sm font-semibold leading-6 text-gray-900"
                      onClick={() => {
                        router.push("/login");
                      }}
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      className="rounded-md bg-sky-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-sky-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-sky-600"
                      onClick={(e) => {
                        e.preventDefault();
                        handleConfirmationModal(formik);
                      }}
                      // onClick={formik.handleSubmit}
                    >
                      Save
                    </button>
                  </div>
                )}
              </dl>
            </div>
          </div>
        </fieldset>
      </form>
    </>
  );
}
