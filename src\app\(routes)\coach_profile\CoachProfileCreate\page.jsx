"use client";
import Address from "@/components/Profile/Address";
import { Dialog, Transition } from "@headlessui/react";
import { useEffect, useState, useRef, Fragment } from "react";
import { useFormik } from "formik";
import * as Yup from "yup";
import dynamic from "next/dynamic";
import axios from "axios";
import Select from "react-select";
import { useSearchDebounce } from "@/helpers/useSearchDebounce";

import "@/style/customStyle.css";

const ReactQuill = dynamic(() => import("react-quill"), {
  ssr: false,
});
import { useRouter } from "next/navigation";

import {
  XMarkIcon,
  PencilSquareIcon,
  TrashIcon,
} from "@heroicons/react/24/solid";
import API from "@/components/API";
import SuccessNotification from "@/components/Notification/SuccessNotification";
import ErrorNotification from "@/components/Notification/ErrorNotification";
import Modal from "@/components/EditImageModal/Modal";
import Image from "next/image";

import VerificationBanner from "@/components/verificationBanner/VerificationBanner";
import ConfirmaitonModal from "@/components/ConfirmationModal/ConfirmationModal";
import { Country, State, City } from "country-state-city";
import TermsAndConditionModal from "@/components/TermsAndCondition/TermsAndConditionModal";
import { MultiSelect } from "react-multi-select-component";

const indianStates = [
  { value: "Andhra Pradesh", label: "Andhra Pradesh" },
  { value: "Arunachal Pradesh", label: "Arunachal Pradesh" },
  { value: "Assam", label: "Assam" },
  { value: "Bihar", label: "Bihar" },
  { value: "Chhattisgarh", label: "Chhattisgarh" },
  { value: "Goa", label: "Goa" },
  { value: "Gujarat", label: "Gujarat" },
  { value: "Haryana", label: "Haryana" },
  { value: "Himachal Pradesh", label: "Himachal Pradesh" },
  { value: "Jharkhand", label: "Jharkhand" },
  { value: "Karnataka", label: "Karnataka" },
  { value: "Kerala", label: "Kerala" },
  { value: "Madhya Pradesh", label: "Madhya Pradesh" },
  { value: "Maharashtra", label: "Maharashtra" },
  { value: "Manipur", label: "Manipur" },
  { value: "Meghalaya", label: "Meghalaya" },
  { value: "Mizoram", label: "Mizoram" },
  { value: "Nagaland", label: "Nagaland" },
  { value: "Odisha", label: "Odisha" },
  { value: "Punjab", label: "Punjab" },
  { value: "Rajasthan", label: "Rajasthan" },
  { value: "Sikkim", label: "Sikkim" },
  { value: "Tamil Nadu", label: "Tamil Nadu" },
  { value: "Telangana", label: "Telangana" },
  { value: "Tripura", label: "Tripura" },
  { value: "Uttar Pradesh", label: "Uttar Pradesh" },
  { value: "Uttarakhand", label: "Uttarakhand" },
  { value: "West Bengal", label: "West Bengal" },
  { value: "Andaman & Nicobar", label: "Andaman & Nicobar" },
  { value: "Chandigarh", label: "Chandigarh" },
  { value: "Daman & Diu", label: "Dadra and Nagar Haveli and Daman & Diu" },
  { value: "Lakshadweep", label: "Lakshadweep" },
  { value: "Delhi", label: "Delhi" },
  { value: "Pondicherry", label: "Pondicherry" },
  { value: "Jammu & Kashmir", label: "Jammu & Kashmir" },
  { value: "Ladakh", label: "Ladakh" },
];
export default function CoachProfileCreate() {
  const [successNotification, setSuccessNotification] = useState(false);
  const [errorNotification, setErrorNotification] = useState(false);
  const [message, setMessage] = useState("");
  const [showModal, setShowModal] = useState(false);
  const [profile, setProfile] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [showEdit, setShowEdit] = useState(false);
  const [isEditable, setIsEditable] = useState(true);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [loading, setLoading] = useState(false);
  const [pincodeError, setPincodeError] = useState(false);
  const [categories, setCategories] = useState([{}]);
  const [selectedImages, setSelectedImages] = useState([]);

  const [verificationModal, setVerificationModal] = useState(false);

  const [selected, setSelected] = useState([]);
  const [categoryError, setCategoryError] = useState(false);
  const [open, setOpen] = useState(false);
  const [saveData, setSaveData] = useState();
  const [states, setStates] = useState([]);

  const [awardImages, setAwardImages] = useState([]);
  const [playingExperienceImages, setPlayingExperienceImages] = useState([]);
  const [coachingExperienceImages, setCoachingExperienceImages] = useState([]);
  const [coachingQualificationsImages, setCoachingQualificationsImages] =
    useState([]);

  const [showConfirmation, setShowConfirmation] = useState(false);

  const [search, setSearch] = useState("");
  const [academyOptions, setAcademyOptions] = useState([]);
  const [showDropdown, setShowDropdown] = useState(false);
  const debouncedSearch = useSearchDebounce(search);
  const dropdownRef = useRef();

  useEffect(() => {
    if (debouncedSearch.trim() === "") {
      setAcademyOptions([]);
      return;
    }

    const fetchAcademies = async () => {
      try {
        const response = await axios.get(`${API}/api/academy`, {
          params: {
            name: debouncedSearch,
            authStatus: "authorized",
            status: "active",
          },
        });
        setAcademyOptions(response.data?.data?.academies || []);
        setShowDropdown(true);
      } catch (err) {
        console.error("Error fetching academies", err);
      }
    };

    fetchAcademies();
  }, [debouncedSearch]);

  // Hide dropdown when clicked outside
  useEffect(() => {
    function handleClickOutside(e) {
      if (dropdownRef.current && !dropdownRef.current.contains(e.target)) {
        setShowDropdown(false);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const handleSelect = (academy) => {
    setSearch(academy.name); // For display
    formik.setFieldValue("academyName", academy.name); // Just for UI
    formik.setFieldValue("academyId", academy._id); // For form submission
    setShowDropdown(false);
    console.log("Selected academy:", academy); // Debug log
    console.log("Formik values after selection:", formik.values); // Debug log
  };

  useEffect(() => {
    const stateData = State.getStatesOfCountry("IN");
    setStates(stateData);
  }, []);
  const phoneRegExp = /^[6-9]\d{9}$/;
  const pinCodeRegExp = /^[1-9][0-9]{5}$/;
  const emailRegExp = /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i;
  const passwordRegExp =
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[^\w\s])[A-Za-z\d!"#$%&'()*+,-./:;<=>?@[\\\]^_`{|}~]{8,}$/;

  const panRegex = /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/;
  const gstRegex = /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/;
  useEffect(() => {
    const stateData = State.getStatesOfCountry("IN");
    setStates(stateData);
  }, []);
  const router = useRouter();
  const getCategories = async () => {
    try {
      let response = await axios.get(`${API}/api/category`);
      // console.log(response.data, "ooooooo");
      setCategories(response.data.data);
    } catch (error) {
      console.log("error 48");
    }
  };
  const updateDB = async (obj) => {
    try {
      let myHeaders = new Headers();
      myHeaders.append("Content-Type", "application/json");

      // const obj = {
      //   coachingQualifications: [...coachingQualificationsImages],
      //   coachingExperience: [...coachingExperienceImages],
      //   award: [...awardImages],
      //   playerExperience: [...playingExperienceImages],
      // };

      let requestOptions = {
        method: "PATCH",
        headers: myHeaders,
        body: JSON.stringify(obj),
      };
      let response = await fetch(`/api/coach_profile`, requestOptions);

      const result = await response.json();
    } catch (error) {
      console.log("error 77");
    }
  };
  const updateDBImage = async ({ images }) => {
    try {
      let myHeaders = new Headers();
      myHeaders.append("Content-Type", "application/json");

      let requestOptions = {
        method: "PATCH",
        headers: myHeaders,
        body: JSON.stringify({ kycDocuments: { documentImg: [...images] } }),
      };
      let response = await fetch(`/api/coach_profile`, requestOptions);

      const result = await response.json();
    } catch (error) {
      console.log("error 45");
    }
  };
  const handleFileChangeImage = async (e, index) => {
    try {
      const file = e.currentTarget.files[0];

      if (file && file.size > 10 * 1024 * 1024) {
        setErrorNotification(true);
        setMessage("Please select a file less than 10 MB.");
        setTimeout(() => {
          setErrorNotification(false);
        }, 3000);
        return;
      }

      const formData = new FormData();
      formData.append("image", file);

      const response = await axios.post(
        `${API}/api/coach/uploadImage`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );

      const url = response?.data?.url;

      await setSelectedImages([...selectedImages, { url: url }]);
      await updateDBImage({ images: [...selectedImages, { url: url }] });

      formik.setFieldValue(`kycDocuments.documentImg.${index}.url`, url);
    } catch (error) {
      console.log("error 82");
    }
  };
  const deleteFilesImage = async (url, index) => {
    try {
      const formData = new FormData();
      formData.append("url", url);

      const response = await axios.post(
        `${API}/api/coach/uploadImage`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );

      const resp = await response?.data;

      const updatedImages = selectedImages.filter((x, idx) => idx !== index);

      await updateDBImage({
        images: updatedImages,
      });

      setSelectedImages(selectedImages.filter((x, idx) => idx !== index));
    } catch (error) {
      console.log("error 111");
    }
  };
  const getCoachDetailsKyc = async () => {
    try {
      let myHeaders = new Headers();
      myHeaders.append("Content-Type", "application/json");

      let requestOptions = {
        method: "GET",
        headers: myHeaders,
      };

      const response = await fetch(`/api/coach_profile`, requestOptions);

      const result = await response.json();
      if (!result.error) {
        formik.setValues({ ...formik.values, ...result });
        setShowEdit(true);
        let tempImages = [];
        await Promise.all(
          result.kycDocuments.documentImg.map(async (x) => {
            x.url &&
              x.url !== "" &&
              tempImages.push({
                url: await x.url,
              });
          })
        );
        setSelectedImages([...selectedImages, ...tempImages]);
        // if (
        //   result.status === "inactive" &&
        //   result.kycDocuments.documentImg.length > 0
        // ) {
        //   setVerificationModal(true);
        //   setMessage(
        //     "Your KYC details have been successfully submitted. Upon approval by the administrator, you will be able to create courses"
        //   );
        // }
        if (
          result.status !== "active" &&
          result.kycDocuments.documentImg.length == 0
        ) {
          setSuccessNotification(true);
          setMessage(
            "Your Profile is authorized. Please add KYC details to proceed."
          );
          setTimeout(() => {
            setSuccessNotification(false);
          }, 5000);
        } else {
          if (!result.kycDocuments.documentImg.length > 0) {
            setVerificationModal(true);
            setMessage(
              "Your details have been saved successfully. Your application is currently being processed, and we will notify you once it has been approved. After approval, you will be able to create courses"
            );
          }
        }
        if (
          !result.kycDocuments.documentNumber ||
          result.kycDocuments.documentNumber === "" ||
          result.kycDocuments.documentImg.length == 0
        ) {
          setIsEditable(true);
        } else {
          setIsEditable(false);
        }
      }
    } catch (error) {
      console.log("error 181");
    }
  };
  useEffect(() => {
    getCategories();
  }, []);
  const calculateAge = async (dob) => {
    const currentDate = new Date();
    const dobDate = new Date(dob);

    let age = currentDate.getFullYear() - dobDate.getFullYear();

    // Check if the current date hasn't reached the birth month and day yet
    if (
      currentDate.getMonth() < dobDate.getMonth() ||
      (currentDate.getMonth() === dobDate.getMonth() &&
        currentDate.getDate() < dobDate.getDate())
    ) {
      age--;
    }

    return age;
  };

  const deleteImageFiles = async (url) => {
    setLoading(true);
    try {
      const formData = new FormData();
      formData.append("url", url);

      const response = await axios.post(
        `${API}/api/coach/uploadImage`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );
      const resp = response?.data;
      formik.setFieldValue("profileImg", "");
      setLoading(false);
      setShowDeleteModal(!showDeleteModal);
      setProfile("");
      if (showEdit) {
        let myHeaders = new Headers();
        myHeaders.append("Content-Type", "application/json");

        let requestOptions = {
          method: "PATCH",
          headers: myHeaders,
          body: JSON.stringify({ profileImg: "" }),
        };
        let response = await fetch(`/api/coach_profile`, requestOptions);

        const result = await response.json();
        setLoading(false);
        setShowDeleteModal(!showDeleteModal);

        // console.log(result, "result of delete image");
      }
      // console.log(resp);
    } catch (error) {
      setLoading(false);
      console.log("error 106");
      setShowDeleteModal(!showDeleteModal);
    }
  };

  function formatDateToYYYYMMDD(dateString) {
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = `0${date.getMonth() + 1}`.slice(-2); // Add leading zero if needed
    const day = `0${date.getDate()}`.slice(-2); // Add leading zero if needed

    return `${year}-${month}-${day}`;
  }

  const getCoachDetails = async () => {
    try {
      let myHeaders = new Headers();
      myHeaders.append("Content-Type", "application/json");

      let requestOptions = {
        method: "GET",
        headers: myHeaders,
      };

      const response = await fetch(`/api/coach_profile`, requestOptions);

      const result = await response.json();
      setProfile(result?.profileImg);
      if (!result.error) {
        setIsEditable(false);
        formik.setValues({ ...formik.values, ...result });
        formik.setFieldValue("dob", formatDateToYYYYMMDD(result?.dob));
        result.linkedFacilities.map((x, i) => {
          formik.setFieldValue(`linkedFacilities[${i}].pincode`, x.pincode);
        });
        setProfile(result?.profileImg);
        setShowEdit(true);
      }
    } catch (error) {
      console.log("error 145");
    }
  };

  const getDetailsFromPincode = async (pincode, index) => {
    try {
      if (!pincode.length > 0) setPincodeError(true);
      formik.setFieldValue(`linkedFacilities[${index}].pinCode`, pincode);
      const details = await axios.get(
        `https://api.postalpincode.in/pincode/${pincode}`
      );
      // console.log(details);
      if (
        details?.data[0]?.Status == "Error" ||
        details?.data[0]?.Status == "404" ||
        details?.data[0]?.Message == "No records found"
      ) {
        setPincodeError(true);
        await formik.setFieldTouched(
          `linkedFacilities[${index}].pinCode`,
          "Pincode is not correct"
        );
        await formik.setFieldError(
          `linkedFacilities[${index}].pinCode`,
          "Pincode is not correct"
        );
        formik.setFieldValue(`linkedFacilities[${index}].city`, "");
        formik.setFieldValue(`linkedFacilities[${index}].state`, "");
        formik.setFieldValue(`linkedFacilities[${index}].country`, "");
        return;
      } else {
        setPincodeError(false);
        formik.setFieldValue(
          `linkedFacilities[${index}].city`,
          `${details?.data[0]?.PostOffice[0]?.Name}, ${details?.data[0]?.PostOffice[0]?.District}`
        );
        formik.setFieldValue(
          `linkedFacilities[${index}].state`,
          details?.data[0]?.PostOffice[0]?.State
        );
        formik.setFieldValue(
          `linkedFacilities[${index}].country`,
          details?.data[0]?.PostOffice[0]?.Country
        );
      }
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    getCoachDetails();
    getCoachDetailsKyc();
  }, []);
  const regExp = /^[0-9]\d{99}$/;
  const formik = useFormik({
    initialValues: {
      firstName: "",
      lastName: "",
      gender: "",
      email: "",
      mobile: "",
      dob: "",
      password: "",
      confirmPassword: "",
      profileImg: "",
      age: "",
      alternateMobile: "",
      linkedFacilities: [
        {
          name: "",
          addressLine1: "",
          addressLine2: "",
          city: "",
          state: "",
          pinCode: "",
          country: "",
          amenities: "",
        },
      ],
      experience: "",
      language: "",
      sportsCategories: [],
      privacyPolicyAccepted: true,
      coachingQualifications: [{ description: "", image: "" }],
      coachingExperience: [{ description: "", image: "" }],
      playerExperience: [{ description: "", image: "" }],
      award: [{ description: "", image: "" }],
      kycDocuments: {
        documentName: "",
        documentNumber: "",
        documentImg: [],
      },
      bankDetails: {
        accountNumber: "",
        accountHolderName: "",
        ifsc: "",
      },
      academyName: "", // shown in input
      academyId: "", // sent to backend
      affiliationType: "", // individual or academy
      coachShare: "",
      academyShare: "",
      availability: {
        startDate: "",
        endDate: "",
        days: [],
        startTime: "",
        endTime: "",
      },
      hasGst: false,
      gstNumber: "",
      gstState: "",
    },
    validationSchema: Yup.object().shape({
      firstName: Yup.string()
        .min(3, "Atleast 3 characters are required")
        .max(100, "Only 30 characters are allowed")
        .required("Name is required"),
      lastName: Yup.string()
        .min(3, "Atleast 3 characters are required")
        .max(100, "Only 30 characters are allowed")
        .required("Last name is required"),
      gender: Yup.string().required("Gender is required"),
      // gstState: Yup.string()
      // .required("GST State is required")
      // .max(100, "Only 100 characters are allowed"),
      email: Yup.string()
        .email("Invalid email")
        .matches(emailRegExp, "Invalid email")
        .required("Email is required"),
      dob: Yup.date()
        .required("Date of birth is required")
        .max(new Date(), "Date of birth cannot be in the future")
        .test("is-adult", "Must be at least 18 years old", function (value) {
          const today = new Date();
          const minAgeDate = new Date(
            today.getFullYear() - 18,
            today.getMonth(),
            today.getDate()
          );
          return value <= minAgeDate;
        }),
      password: !showEdit
        ? Yup.string()
            .matches(
              passwordRegExp,
              "Password must contain minimum eight characters, at least one uppercase letter, one lowercase letter, one number, and one special character."
            )
            .required("Password is required")
            .max(30, "Only 30 characters are allowed")
        : Yup.string(),
      confirmPassword: !showEdit
        ? Yup.string()
            .required("Password is required")
            .oneOf([Yup.ref("password"), null], "Passwords must match")
        : Yup.string(),
      mobile: Yup.string()
        .matches(phoneRegExp, "Phone number is not valid")
        .required("Please enter your phone number"),
      alternateMobile: Yup.string().matches(
        phoneRegExp,
        "Phone number is not valid"
      ),
      // .required("Please enter your phone number"),
      linkedFacilities: Yup.array().of(
        Yup.object().shape({
          name: Yup.string()
            .required("Name is required")
            .max(100, "Only 100 characters are allowed")
            .min(3, "Minimun 3 characters are required"),
          addressLine1: Yup.string().required("Address Line 1 is required"),
          addressLine2: Yup.string(),
          city: Yup.string()
            .required("City is required")
            .max(100, "Only 100 characters are allowed"),
          state: Yup.string()
            .required("State is required")
            .max(100, "Only 100 characters are allowed"),
          pinCode: Yup.string()
            .matches(pinCodeRegExp, "PIN code is not valid")
            .required("Pincode is required"),
          country: Yup.string()
            .required("Country is required")
            .max(100, "Only 100 characters are allowed"),
          // phone: Yup.string().matches(phoneRegExp, "Phone number is not valid"),
          amenities: Yup.string(),
        })
      ),
      kycDocuments: Yup.object().shape({
        //  documentName: Yup.string().required("Document Name is required"),
        documentNumber: Yup.string()
          .matches(
            panRegex,
            "Must be a valid pan number with all upper case character"
          )
          .required("Document Number is required"),
        documentImg: Yup.array().of(
          Yup.object().shape({
            url: Yup.string().url("Invalid URL"),
          })
        ),
      }),
      bankDetails: Yup.object().shape({
        accountNumber: Yup.string()
          .max(20, "Not more than 20 characters")
          .required("Account number is required"),
        accountHolderName: Yup.string()
          .max(100, "Only 100 characters are allowed")
          .required("Account holder name is required"),
        ifsc: Yup.string()
          .max(20, "Only 30 characters are allowed")
          .required("IFSC Code is required"),
      }),
      hasGst: Yup.boolean(),

      gstState: Yup.string(),
      gstNumber: Yup.string()
        .matches(gstRegex, "Please enter a valid GST number")
        .when("hasGst", {
          is: true,
          then: (schema) => schema.required("GST Number is required"),
          otherwise: (schema) => schema.notRequired(),
        }),
      experience: Yup.number("Only Number are allowed")
        .max(99, "can not more than 99 years")
        .required("This field is required"),
      language: Yup.string().required("Atleast one language is required"),
      affiliationType: Yup.string().oneOf(["individual", "academy"]).required(),

      academy: Yup.string().when("affiliationType", {
        is: "academy",
        then: Yup.string().required("Academy is required"),
      }),

      coachShare: Yup.number().when("affiliationType", {
        is: "academy",
        then: Yup.number()
          .required()
          .min(0)
          .max(100)
          .test(
            "sumCheck",
            "Coach + Academy share must equal 100%",
            function (value) {
              return value + this.parent.academyShare === 100;
            }
          ),
      }),

      academyShare: Yup.number().when("affiliationType", {
        is: "academy",
        then: Yup.number().required().min(0).max(100),
      }),

      availability: Yup.object().when("affiliationType", {
        is: "academy",
        then: Yup.object().shape({
          startDate: Yup.date().required("Start Date is required"),
          endDate: Yup.date().nullable(),
          days: Yup.array().min(1, "Select at least one day"),
          startTime: Yup.string().required(),
          endTime: Yup.string().required(),
        }),
      }),
    }),

    onSubmit: async (values) => {
      try {
        if (pincodeError) {
          setErrorNotification(true);
          setMessage("Pincode is not correct");
          setTimeout(() => {
            setErrorNotification(false);
          }, 3000);
          return;
        }

        if (values.kycDocuments.documentImg.length === 0) {
          setErrorNotification(true);
          setMessage("Please upload the document images");
          setTimeout(() => {
            setErrorNotification(false);
          }, 3000);
          return;
        }

        setLoading(true);
        const age = await calculateAge(values.dob);
        await formik.setFieldValue("age", age);

        setErrorNotification(false);
        await formik.setFieldValue("profileImg", profile || "");

        if (selected.length === 0) {
          setCategoryError(true);
          setLoading(false);
          return;
        }

        let temp = selected.map((x) => x.value);
        formik.setFieldValue("sportsCategories", temp);

        const filterEmptyFields = (array) => {
          return array.filter((item) => item.description || item.image);
        };

        const filteredValues = {
          coachingQualifications: filterEmptyFields(
            values.coachingQualifications
          ),
          coachingExperience: filterEmptyFields(values.coachingExperience),
          playerExperience: filterEmptyFields(values.playerExperience),
          award: filterEmptyFields(values.award),
        };

        let myHeaders = new Headers();
        myHeaders.append("Content-Type", "application/json");

        let requestOptions = {
          method: showEdit ? "PATCH" : "POST",
          headers: myHeaders,
          body: JSON.stringify({ ...values, ...filteredValues, age }),
        };

        let response = await fetch(
          showEdit ? `/api/coach_profile` : `/api/signup`,
          requestOptions
        );

        const result = await response.json();

        if (!result.error) {
          setLoading(false);
          setSuccessNotification(true);
          setErrorNotification(false);
          setMessage("Details Saved Successfully");
          setIsEditable(false);

          if (result.status === "active") {
            setTimeout(() => {
              setSuccessNotification(false);
              router.push(
                showEdit ? "/calendar" : "/profile/professional_details#signup"
              );
            }, 3000);
          } else {
            setSuccessNotification(false);
            setVerificationModal(true);
            setMessage(
              "Details Saved Successfully. Your application is under process and we will inform you once approved"
            );
          }
        } else {
          setLoading(false);
          setSuccessNotification(false);
          setErrorNotification(true);
          setIsEditable(true);

          if (result.error.status === 400) {
            setMessage("Coach with same mobile or email already exists");
          } else {
            setMessage(result.error);
          }

          setTimeout(() => {
            setErrorNotification(false);
          }, 3000);
        }
      } catch (error) {
        console.error("Error:", error);
        setLoading(false);
        setMessage("Please try again");
        setSuccessNotification(false);
        setErrorNotification(true);
        setTimeout(() => {
          setErrorNotification(false);
        }, 3000);
      }
    },
  });
  const handleFileChange = async (e, fieldName, index) => {
    // console.log(e, fieldName, index, "oo");
    try {
      const file = e.currentTarget.files[0];

      if (file && file.size > 10 * 1024 * 1024) {
        setErrorNotification(true);
        setMessage("Please select a file less than 10 MB.");
        setTimeout(() => {
          setErrorNotification(false);
        }, 3000);
        return;
      }

      const formData = new FormData();
      formData.append("image", file);

      const response = await axios.post(
        `${API}/api/coach/uploadImage`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );

      const url = response?.data?.url;

      let obj = {
        coachingQualifications: [...coachingQualificationsImages],
        coachingExperience: [...coachingExperienceImages],
        award: [...awardImages],
        playerExperience: [...playingExperienceImages],
      };

      if (fieldName === "award") {
        await formik.setFieldValue(`${fieldName}.${index}.image`, url);

        const updatedAwardImages = [...awardImages];
        updatedAwardImages[index] = {
          description: formik.values.award[index].description,
          image: url,
        };
        setAwardImages([...updatedAwardImages]);
        obj = {
          coachingQualifications: [...coachingQualificationsImages],
          coachingExperience: [...coachingExperienceImages],
          award: updatedAwardImages,
          playerExperience: [...playingExperienceImages],
        };
      }

      if (fieldName === "playerExperience") {
        await formik.setFieldValue(`${fieldName}.${index}.image`, url);
        const updatedPlayingExperienceImages = [...playingExperienceImages];
        updatedPlayingExperienceImages[index] = {
          description: formik.values.playerExperience[index].description,
          image: url,
        };
        setPlayingExperienceImages([...updatedPlayingExperienceImages]);
        obj = {
          coachingQualifications: [...coachingQualificationsImages],
          coachingExperience: [...coachingExperienceImages],
          award: [...awardImages],
          playerExperience: updatedPlayingExperienceImages,
        };
      }
      if (fieldName === "coachingExperience") {
        await formik.setFieldValue(`${fieldName}.${index}.image`, url);

        const updatedCoachingExperienceImages = [...coachingExperienceImages];
        updatedCoachingExperienceImages[index] = {
          description: formik.values.coachingExperience[index].description,
          image: url,
        };
        setCoachingExperienceImages([...updatedCoachingExperienceImages]);

        obj = {
          coachingQualifications: [...coachingQualificationsImages],
          coachingExperience: updatedCoachingExperienceImages,
          award: [...awardImages],
          playerExperience: [...playingExperienceImages],
        };
      }
      if (fieldName === "coachingQualifications") {
        await formik.setFieldValue(`${fieldName}.${index}.image`, url);

        const updatedCoachingQualificationsImages = [
          ...coachingQualificationsImages,
        ];
        updatedCoachingQualificationsImages[index] = {
          description: formik.values.coachingQualifications[index].description,
          image: url,
        };
        setCoachingQualificationsImages([
          ...updatedCoachingQualificationsImages,
        ]);
        obj = {
          coachingQualifications: updatedCoachingQualificationsImages,
          coachingExperience: [...coachingExperienceImages],
          award: [...awardImages],
          playerExperience: [...playingExperienceImages],
        };
      }

      await updateDB(obj);
    } catch (error) {
      console.log(error.response.data.error);
    }
  };
  const addCoachingExperience = () => {
    formik.setValues({
      ...formik.values,
      coachingExperience: [
        ...formik.values.coachingExperience,
        {
          description: "",
          image: "",
        },
      ],
    });
    setCoachingExperienceImages([
      ...coachingExperienceImages,
      {
        description: "",
        image: "",
      },
    ]);
  };

  const addPlayingExperience = () => {
    formik.setValues({
      ...formik.values,
      playerExperience: [
        ...formik.values.playerExperience,
        {
          playerExperience: "",
          image: "",
        },
      ],
    });
    setPlayingExperienceImages([
      ...playingExperienceImages,
      {
        description: "",
        image: "",
      },
    ]);
  };
  const addAwards = () => {
    formik.setValues({
      ...formik.values,
      award: [
        ...formik.values.award,
        {
          awardName: "",
          image: "",
        },
      ],
    });
    setAwardImages([
      ...awardImages,
      {
        description: "",
        image: "",
      },
    ]);
  };
  const addQualification = () => {
    formik.setValues({
      ...formik.values,
      coachingQualifications: [
        ...formik.values.coachingQualifications,
        {
          description: "",
          image: "",
        },
      ],
    });
    setCoachingQualificationsImages([
      ...coachingQualificationsImages,
      {
        description: "",
        image: "",
      },
    ]);
  };
  const addAddress = () => {
    formik.setValues({
      ...formik.values,
      linkedFacilities: [
        ...formik.values.linkedFacilities,
        {
          name: "",
          addressLine1: "",
          addressLine2: "",
          city: "",
          state: "",
          pinCode: "",
          country: "",
          // phone: "",
          amenities: "",
        },
      ],
    });
    // setIsFacilities(true);
  };
  const handleConfirmationModal = (formik) => {
    setOpen(true);
    setSaveData(formik);
  };
  const handlePolicyModal = (formik) => {
    if (Object.keys(formik.errors).length === 0 && selected.length !== 0) {
      console.log(formik.values, "formik values");
      setSaveData(formik);
      setOpen(true);
    } else {
      if (selected.length == 0) {
        setCategoryError(true);
        setLoading(false);
      }
      formik.handleSubmit();
    }
  };

  return (
    <>
      <title>
        {!showEdit
          ? "Coach Signup - Basic details"
          : "Coach Profile - Basic details"}
      </title>
      <DeleteDialog
        showDeleteModal={showDeleteModal}
        setShowDeleteModal={setShowDeleteModal}
        deleteImageFiles={deleteImageFiles}
        profile={profile}
        loading={loading}
        setLoading={setLoading}
      />

      {successNotification && <SuccessNotification message={message} />}

      {errorNotification && <ErrorNotification message={message} />}

      {showModal && (
        <Modal
          setProfile={setProfile}
          setFieldValue={formik.setFieldValue}
          setShowModal={setShowModal}
          showModal={showModal}
          setErrorNotification={setErrorNotification}
          setMessage={setMessage}
          showEdit={showEdit}
          setShowEdit={setShowEdit}
        />
      )}

      <form onSubmit={formik.handleSubmit}>
        {/* <fieldset disabled={!isEditable}> */}
        <div className="overflow-hidden bg-white shadow sm:rounded-lg">
          {/* <h1 className="flex justify-center"> Basic Details</h1> */}
          <div className="border-t border-gray-100">
            <dl className="divide-y divide-gray-100">
              <div className="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 ">
                <dt className="text-base font-semibold tracking-wide  text-gray-900 required">
                  Profile Image
                </dt>
                <dd className="mt-1 leading-6 text-gray-700 sm:col-span-2 sm:mt-0 flex justify-center md:justify-normal gap-x-3">
                  <div className="w-[40%]">
                    <img
                      className="relative inline-block w-[200px] h-[150px] md:h-[200px] object-cover rounded-full object-center"
                      // src={preview}
                      src={
                        profile && profile !== ""
                          ? profile
                          : "https://media.istockphoto.com/id/1300845620/vector/user-icon-flat-isolated-on-white-background-user-symbol-vector-illustration.jpg?s=612x612&w=0&k=20&c=yBeyba0hUkh14_jgv1OKqIH0CCSWU_4ckRkAoy2p73o="
                      }
                      alt="Profile Image"
                    />
                  </div>
                  <div className="flex gap-x-2 items-end">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        e.preventDefault();
                        setShowModal(true);
                      }}
                    >
                      <PencilSquareIcon
                        className="h-7 w-7"
                        aria-hidden="true"
                      />
                    </button>

                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        e.preventDefault();
                        if (profile) {
                          setShowDeleteModal(true);
                        }
                      }}
                      disabled={!profile} // Disable button if profile is empty
                      className={`${
                        !profile
                          ? "cursor-not-allowed opacity-50" // Style for the disabled state
                          : ""
                      }`}
                    >
                      <TrashIcon className="h-7 w-7" aria-hidden="true" />
                    </button>
                  </div>
                </dd>
              </div>
              <div className="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt className="text-base font-semibold tracking-wide text-gray-900">
                  Personal Information
                </dt>
                <dd className="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                  <div className="grid grid-cols-1 gap-x-8 gap-y-8  md:grid-cols-1">
                    <div>
                      <div className="grid max-w-2xl grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
                        <div className="sm:col-span-3">
                          <label
                            htmlFor="firstName"
                            className="block text-[16px ]font-medium leading-6 text-gray-900 capitalize required"
                          >
                            First name
                          </label>
                          <div className="mt-2">
                            <input
                              type="text"
                              name="firstName"
                              required
                              id="firstName"
                              {...formik.getFieldProps("firstName")}
                              autoComplete="given-name"
                              className="px-3 block w-full rounded-md border-0 py-1.5 text-gray-900  shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                            />
                            {formik.touched.firstName &&
                              formik.errors.firstName && (
                                <div className="text-red-500">
                                  {formik.errors.firstName}
                                </div>
                              )}
                          </div>
                        </div>

                        <div className="sm:col-span-3">
                          <label
                            htmlFor="lastName"
                            className="block text-[16px ]font-medium leading-6 text-gray-900 capitalize required"
                          >
                            Last name
                          </label>
                          <div className="mt-2">
                            <input
                              type="text"
                              name="lastName"
                              id="lastName"
                              autoComplete="family-name"
                              {...formik.getFieldProps("lastName")}
                              className="px-3 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                            />
                            {formik.touched.lastName &&
                              formik.errors.lastName && (
                                <div className="text-red-500">
                                  {formik.errors.lastName}
                                </div>
                              )}
                          </div>
                        </div>

                        <div className="sm:col-span-3">
                          <label
                            htmlFor="mobile"
                            className="block text-[16px ]font-medium leading-6 text-gray-900 capitalize required"
                          >
                            Mobile
                          </label>
                          <div className="mt-2 flex items-center ">
                            <span className="px-3 py-1.5 border border-gray-300 bg-gray-100 rounded-l-md text-gray-500">
                              +91
                            </span>
                            <input
                              type="number"
                              name="mobile"
                              disabled={!isEditable}
                              id="mobile"
                              onFocus={(e) =>
                                e.target.addEventListener(
                                  "wheel",
                                  function (e) {
                                    e.preventDefault();
                                  },
                                  { passive: false }
                                )
                              }
                              autoComplete="given-name"
                              {...formik.getFieldProps("mobile")}
                              className="px-3 block w-full rounded-r-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                            />
                            <style jsx>{`
                              input[type="number"]::-webkit-inner-spin-button,
                              input[type="number"]::-webkit-outer-spin-button {
                                -webkit-appearance: none;
                                margin: 0;
                              }

                              input[type="number"] {
                                -moz-appearance: textfield;
                              }
                            `}</style>
                            {formik.touched.mobile && formik.errors.mobile && (
                              <div className="text-red-500">
                                {formik.errors.mobile}
                              </div>
                            )}
                          </div>
                        </div>

                        <div className="sm:col-span-3">
                          <label
                            htmlFor="alternateMobile"
                            className="block text-[16px ]font-medium leading-6 text-gray-900 capitalize"
                          >
                            Alternate Mobile Number
                          </label>
                          <div className="mt-2 flex items-center">
                            <span className="px-3 py-1.5 border border-gray-300 bg-gray-100 rounded-l-md text-gray-500">
                              +91
                            </span>
                            <input
                              type="number"
                              name="alternateMobile"
                              onFocus={(e) =>
                                e.target.addEventListener(
                                  "wheel",
                                  function (e) {
                                    e.preventDefault();
                                  },
                                  { passive: false }
                                )
                              }
                              id="alternateMobile"
                              autoComplete="given-name"
                              {...formik.getFieldProps("alternateMobile")}
                              className="px-3 block w-full rounded-r-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                            />
                            <style jsx>{`
                              input[type="number"]::-webkit-inner-spin-button,
                              input[type="number"]::-webkit-outer-spin-button {
                                -webkit-appearance: none;
                                margin: 0;
                              }

                              input[type="number"] {
                                -moz-appearance: textfield;
                              }
                            `}</style>
                            {formik.touched.alternateMobile &&
                              formik.errors.alternateMobile && (
                                <div className="text-red-500">
                                  {formik.errors.alternateMobile}
                                </div>
                              )}
                          </div>
                        </div>

                        <div className="sm:col-span-3">
                          <label
                            htmlFor="email"
                            className="block text-[16px ]font-medium leading-6 text-gray-900 capitalize required"
                          >
                            Email address
                          </label>
                          <div className="mt-2">
                            <input
                              id="email"
                              name="email"
                              disabled={!isEditable}
                              type="email"
                              autoComplete="email"
                              {...formik.getFieldProps("email")}
                              className="px-3 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                            />
                            {formik.touched.email && formik.errors.email && (
                              <div className="text-red-500">
                                {formik.errors.email}
                              </div>
                            )}
                          </div>
                        </div>

                        <div className="sm:col-span-3">
                          <label
                            htmlFor="dob"
                            className="block text-[16px ]font-medium leading-6 text-gray-900 capitalize required"
                          >
                            Date of birth
                          </label>
                          <div className="mt-2">
                            <input
                              id="dob"
                              name="dob"
                              type="date"
                              max={formatDateToYYYYMMDD(new Date())}
                              autoComplete="off"
                              {...formik.getFieldProps("dob")}
                              className="px-3 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                            />
                            {formik.touched.dob && formik.errors.dob && (
                              <div className="text-red-500">
                                {formik.errors.dob}
                              </div>
                            )}
                          </div>
                        </div>

                        {!showEdit && (
                          <>
                            <div className="sm:col-span-3">
                              <label
                                htmlFor="password"
                                className="block text-[16px ]font-medium leading-6 text-gray-900 capitalize required"
                              >
                                Password
                              </label>
                              <div className="mt-2">
                                <div className="flex flex-row">
                                  <input
                                    id="password"
                                    name="password"
                                    type={showPassword ? "text" : "password"}
                                    autoComplete="off"
                                    {...formik.getFieldProps("password")}
                                    className="px-3 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                  />
                                  <Image
                                    height={15}
                                    width={15}
                                    style={{ marginLeft: "-30px" }}
                                    src={
                                      showPassword
                                        ? "/showEye.svg"
                                        : "/hideEye.svg"
                                    }
                                    alt=""
                                    onClick={() => {
                                      // console.log("oooo");
                                      setShowPassword(!showPassword);
                                    }}
                                  />
                                </div>
                                {formik.touched.password &&
                                  formik.errors.password && (
                                    <div className="text-red-500">
                                      {formik.errors.password}
                                    </div>
                                  )}
                              </div>
                            </div>

                            <div className="sm:col-span-3">
                              <label
                                htmlFor="confirmPassword"
                                className="block text-[16px ]font-medium leading-6 text-gray-900 capitalize required"
                              >
                                Confirm Password
                              </label>
                              <div className="mt-2">
                                <div className="flex flex-row">
                                  <input
                                    id="confirmPassword"
                                    name="confirmPassword"
                                    type={
                                      showConfirmPassword ? "text" : "password"
                                    }
                                    autoComplete="off"
                                    {...formik.getFieldProps("confirmPassword")}
                                    className="px-3 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                  />
                                  <Image
                                    height={15}
                                    width={15}
                                    style={{ marginLeft: "-30px" }}
                                    src={
                                      showConfirmPassword
                                        ? "/showEye.svg"
                                        : "/hideEye.svg"
                                    }
                                    alt=""
                                    onClick={() => {
                                      // console.log("oooo");
                                      setShowConfirmPassword(
                                        !showConfirmPassword
                                      );
                                    }}
                                  />
                                </div>
                                {formik.touched.confirmPassword &&
                                  formik.errors.confirmPassword && (
                                    <div className="text-red-500">
                                      {formik.errors.confirmPassword}
                                    </div>
                                  )}
                              </div>
                            </div>
                          </>
                        )}

                        <div className="sm:col-span-3">
                          <label
                            htmlFor="gender "
                            className="block text-[16px] font-medium leading-6 text-gray-900 capitalize required"
                          >
                            Gender
                          </label>
                          <div className="mt-2">
                            <select
                              type="text"
                              name="gender"
                              id="gender"
                              autoComplete="family-name"
                              {...formik.getFieldProps("gender")}
                              className="px-3 block w-full rounded-md border-0 py-1.5 h-9 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                            >
                              <option value="">Select Gender</option>
                              <option value="male">Male</option>
                              <option value="female">Female</option>
                              <option value="other">Other</option>
                            </select>
                            {formik.touched.gender && formik.errors.gender && (
                              <div className="text-red-500">
                                {formik.errors.gender}
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </dd>
              </div>
              <div className="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt className="text-base font-semibold tracking-wide text-gray-900">
                  Affiliation Details
                </dt>
                <dd className="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                  <div className="grid grid-cols-1 gap-x-8 gap-y-8 md:grid-cols-1">
                    <div className="grid max-w-2xl grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
                      {/* Affiliation Type Radio */}
                      <div className="sm:col-span-6">
                        <label className="block text-[16px] font-medium leading-6 text-gray-900 required">
                          Are you a part of an academy or working individually?
                        </label>
                        <div className="mt-2 flex gap-4">
                          <label className="flex items-center gap-2">
                            <input
                              type="radio"
                              name="affiliationType"
                              value="individual"
                              checked={
                                formik.values.affiliationType === "individual"
                              }
                              onChange={formik.handleChange}
                            />
                            Working Individually
                          </label>
                          <label className="flex items-center gap-2">
                            <input
                              type="radio"
                              name="affiliationType"
                              value="academy"
                              checked={
                                formik.values.affiliationType === "academy"
                              }
                              onChange={formik.handleChange}
                            />
                            Joining via Academy
                          </label>
                        </div>
                      </div>

                      {/* Conditional Fields for Academy */}
                      {formik.values.affiliationType === "academy" && (
                        <>
                          <div
                            className="sm:col-span-3 relative"
                            ref={dropdownRef}
                          >
                            <label className="block text-[16px] font-medium leading-6 text-gray-900 required">
                              Select Academy
                            </label>
                            <div className="mt-2">
                              <input
                                type="text"
                                placeholder="Type to search..."
                                value={search}
                                onChange={(e) => setSearch(e.target.value)}
                                className="px-3 block w-full rounded-md border-0 py-1.5 h-9 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-indigo-600 sm:text-sm"
                              />

                              {formik.touched.academy &&
                                formik.errors.academy && (
                                  <div className="text-red-500 mt-1">
                                    {formik.errors.academy}
                                  </div>
                                )}

                              {showDropdown && academyOptions.length > 0 && (
                                <ul className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm">
                                  {academyOptions.map((academy) => (
                                    <li
                                      key={academy._id}
                                      className="cursor-pointer px-4 py-2 hover:bg-indigo-100"
                                      onClick={() => handleSelect(academy)}
                                    >
                                      {academy.name}
                                    </li>
                                  ))}
                                </ul>
                              )}
                            </div>
                          </div>

                          <div className="sm:col-span-3">
                            <label className="block text-[16px] font-medium leading-6 text-gray-900 required">
                              Coach Share (%)
                            </label>
                            <div className="mt-2">
                              <input
                                type="number"
                                name="coachShare"
                                {...formik.getFieldProps("coachShare")}
                                className="px-3 block w-full rounded-md border-0 py-1.5 text-gray-900  shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                              />
                              {formik.touched.coachShare &&
                                formik.errors.coachShare && (
                                  <div className="text-red-500">
                                    {formik.errors.coachShare}
                                  </div>
                                )}
                            </div>
                          </div>

                          <div className="sm:col-span-3">
                            <label className="block text-[16px] font-medium leading-6 text-gray-900 required">
                              Academy Share (%)
                            </label>
                            <div className="mt-2">
                              <input
                                type="number"
                                name="academyShare"
                                {...formik.getFieldProps("academyShare")}
                                className="px-3 block w-full rounded-md border-0 py-1.5 text-gray-900  shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                              />
                              {formik.touched.academyShare &&
                                formik.errors.academyShare && (
                                  <div className="text-red-500">
                                    {formik.errors.academyShare}
                                  </div>
                                )}
                            </div>
                          </div>

                          <div className="sm:col-span-3">
                            <label className="block text-[16px] font-medium leading-6 text-gray-900 required">
                              Start Date
                            </label>
                            <div className="mt-2">
                              <input
                                type="date"
                                name="availability.startDate"
                                {...formik.getFieldProps(
                                  "availability.startDate"
                                )}
                                className="px-3 block w-full rounded-md border-0 py-1.5 text-gray-900  shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                              />
                              {formik.touched.availability?.startDate &&
                                formik.errors.availability?.startDate && (
                                  <div className="text-red-500">
                                    {formik.errors.availability.startDate}
                                  </div>
                                )}
                            </div>
                          </div>

                          <div className="sm:col-span-3">
                            <label className="block text-[16px] font-medium leading-6 text-gray-900">
                              End Date (optional)
                            </label>
                            <div className="mt-2">
                              <input
                                type="date"
                                name="availability.endDate"
                                {...formik.getFieldProps(
                                  "availability.endDate"
                                )}
                                className="px-3 block w-full rounded-md border-0 py-1.5 text-gray-900  shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                              />
                            </div>
                          </div>

                          <div className="sm:col-span-3">
                            <label className="block text-[16px] font-medium leading-6 text-gray-900 required">
                              Days of Week
                            </label>
                            <div className="mt-2 flex flex-wrap gap-3">
                              <label className="flex items-center gap-2">
                                <input
                                  type="checkbox"
                                  checked={
                                    (formik.values.availability?.days || [])
                                      .length === 7
                                  }
                                  onChange={(e) => {
                                    const allDays = [
                                      "Mon",
                                      "Tue",
                                      "Wed",
                                      "Thu",
                                      "Fri",
                                      "Sat",
                                      "Sun",
                                    ];
                                    formik.setFieldValue(
                                      "availability.days",
                                      e.target.checked ? allDays : []
                                    );
                                  }}
                                />
                                Select All
                              </label>

                              {[
                                "Mon",
                                "Tue",
                                "Wed",
                                "Thu",
                                "Fri",
                                "Sat",
                                "Sun",
                              ].map((day) => (
                                <label
                                  key={day}
                                  className="flex items-center gap-2"
                                >
                                  <input
                                    type="checkbox"
                                    name="availability.days"
                                    value={day}
                                    checked={(
                                      formik.values.availability?.days || []
                                    ).includes(day)}
                                    onChange={(e) => {
                                      const { checked, value } = e.target;
                                      const updated = checked
                                        ? [
                                            ...formik.values.availability.days,
                                            value,
                                          ]
                                        : formik.values.availability.days.filter(
                                            (d) => d !== value
                                          );
                                      formik.setFieldValue(
                                        "availability.days",
                                        updated
                                      );
                                    }}
                                  />
                                  {day}
                                </label>
                              ))}
                            </div>
                            {formik.touched.availability?.days &&
                              formik.errors.availability?.days && (
                                <div className="text-red-500">
                                  {formik.errors.availability.days}
                                </div>
                              )}
                          </div>

                          <div className="sm:col-span-3">
                            <label className="block text-[16px] font-medium leading-6 text-gray-900 required">
                              Start Time
                            </label>
                            <div className="mt-2">
                              <input
                                type="time"
                                name="availability.startTime"
                                {...formik.getFieldProps(
                                  "availability.startTime"
                                )}
                                className="px-3 block w-full rounded-md border-0 py-1.5 text-gray-900  shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                              />
                              {formik.touched.availability?.startTime &&
                                formik.errors.availability?.startTime && (
                                  <div className="text-red-500">
                                    {formik.errors.availability.startTime}
                                  </div>
                                )}
                            </div>
                          </div>

                          <div className="sm:col-span-3">
                            <label className="block text-[16px] font-medium leading-6 text-gray-900 required">
                              End Time
                            </label>
                            <div className="mt-2">
                              <input
                                type="time"
                                name="availability.endTime"
                                {...formik.getFieldProps(
                                  "availability.endTime"
                                )}
                                className="px-3 block w-full rounded-md border-0 py-1.5 text-gray-900  shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                              />
                              {formik.touched.availability?.endTime &&
                                formik.errors.availability?.endTime && (
                                  <div className="text-red-500">
                                    {formik.errors.availability.endTime}
                                  </div>
                                )}
                            </div>
                          </div>
                        </>
                      )}
                    </div>
                  </div>
                </dd>
              </div>

              <div className="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt className="text-base font-semibold tracking-wide text-gray-900">
                  Address
                </dt>
                <dd className="mt-2 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                  <div>
                    {formik.values.linkedFacilities.map((address, index) => (
                      <Fragment key={index}>
                        <div
                        // className="px-4 py-4 grid grid-cols-1 gap-x-8 gap-y-8  md:grid-cols-1 bg-white shadow-sm ring-1 ring-gray-900/5  mb-3"
                        >
                          <div
                            style={{
                              display: "flex",
                              flexDirection: "row",
                              justifyContent: "space-between",
                              borderBottom: "1px solid",
                            }}
                          >
                            <p>Address{index + 1}</p>
                            {index + 1 > 1 ? (
                              <button
                                type="button"
                                className="rounded-full bg-red-600 p-1.5 text-white shadow-sm hover:bg-red-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
                                onClick={() =>
                                  formik.setValues((prevState) => ({
                                    ...prevState,
                                    linkedFacilities:
                                      prevState.linkedFacilities.filter(
                                        (_, i) => i !== index
                                      ),
                                  }))
                                }
                              >
                                <XMarkIcon
                                  className="h-5 w-5"
                                  aria-hidden="true"
                                />
                              </button>
                            ) : null}
                          </div>
                          <div>
                            {/* Add address */}
                            <div className="grid max-w-2xl grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6 mt-3">
                              <div className="sm:col-span-3">
                                <label
                                  htmlFor={`linkedFacilities.${index}.name`}
                                  className="block text-[16px ]font-medium leading-6 text-gray-900 capitalize required"
                                >
                                  Facility Name
                                </label>
                                <div className="mt-2">
                                  <input
                                    type="text"
                                    name={`linkedFacilities.${index}.name`}
                                    id={`linkedFacilities.${index}.name`}
                                    autoComplete="given-name"
                                    {...formik.getFieldProps(
                                      `linkedFacilities.${index}.name`
                                    )}
                                    className="px-3 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                  />
                                  {formik.touched.linkedFacilities?.[index]
                                    ?.name &&
                                    formik.errors.linkedFacilities?.[index]
                                      ?.name && (
                                      <div className="text-red-500">
                                        {
                                          formik.errors.linkedFacilities?.[
                                            index
                                          ]?.name
                                        }
                                      </div>
                                    )}
                                </div>
                              </div>

                              <div className="sm:col-span-3">
                                <label
                                  htmlFor={`linkedFacilities.${index}.pincode`}
                                  className="block text-[16px ]font-medium leading-6 text-gray-900 capitalize required"
                                >
                                  Pincode
                                </label>
                                <div className="mt-2">
                                  <input
                                    name={`linkedFacilities.${index}.pinCode`}
                                    id={`linkedFacilities.${index}.pinCode`}
                                    type="number"
                                    autoComplete="family-name"
                                    value={
                                      formik.values.linkedFacilities[index]
                                        .pinCode
                                    }
                                    onChange={(e) => {
                                      // console.log(e.target.value);
                                      getDetailsFromPincode(
                                        e.target.value,
                                        index
                                      );
                                    }}
                                    onFocus={(e) =>
                                      e.target.addEventListener(
                                        "wheel",
                                        function (e) {
                                          e.preventDefault();
                                        },
                                        { passive: false }
                                      )
                                    }
                                    // {...formik.getFieldProps(
                                    //   `linkedFacilities.${index}.pinCode`
                                    // )}
                                    className="px-3 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                  />
                                  <style jsx>{`
                                    input[type="number"]::-webkit-inner-spin-button,
                                    input[type="number"]::-webkit-outer-spin-button {
                                      -webkit-appearance: none;
                                      margin: 0;
                                    }

                                    input[type="number"] {
                                      -moz-appearance: textfield;
                                    }
                                  `}</style>
                                  {((formik.touched.linkedFacilities?.[index]
                                    ?.pinCode &&
                                    formik.errors.linkedFacilities?.[index]
                                      ?.pinCode) ||
                                    pincodeError) && (
                                    <div className="text-red-500">
                                      {
                                        formik.errors.linkedFacilities?.[index]
                                          ?.pinCode
                                      }
                                    </div>
                                  )}
                                </div>
                              </div>

                              <div className="sm:col-span-3">
                                <label
                                  htmlFor={`linkedFacilities.${index}.addressLine1`}
                                  className="block text-[16px ]font-medium leading-6 text-gray-900 capitalize required"
                                >
                                  Address Line 1
                                </label>
                                <div className="mt-2">
                                  <input
                                    type="text"
                                    name={`linkedFacilities.${index}.addressLin1`}
                                    id={`linkedFacilities.${index}.addressLine1`}
                                    autoComplete="given-name"
                                    {...formik.getFieldProps(
                                      `linkedFacilities.${index}.addressLine1`
                                    )}
                                    className="px-3 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                  />
                                  {formik.touched.linkedFacilities?.[index]
                                    ?.addressLine1 &&
                                    formik.errors.linkedFacilities?.[index]
                                      ?.addressLine1 && (
                                      <div className="text-red-500">
                                        {
                                          formik.errors.linkedFacilities?.[
                                            index
                                          ]?.addressLine1
                                        }
                                      </div>
                                    )}
                                </div>
                              </div>

                              <div className="sm:col-span-3">
                                <label
                                  htmlFor={`linkedFacilities.${index}.addressLine2`}
                                  className="block text-[16px ]font-medium leading-6 text-gray-900 capitalize"
                                >
                                  Address Line 2
                                </label>
                                <div className="mt-2">
                                  <input
                                    type="text"
                                    name={`linkedFacilities.${index}.addressLine2`}
                                    id={`linkedFacilities.${index}.addressLine2`}
                                    autoComplete="family-name"
                                    {...formik.getFieldProps(
                                      `linkedFacilities.${index}.addressLine2`
                                    )}
                                    className="px-3 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                  />
                                  {formik.touched.linkedFacilities?.[index]
                                    ?.addressLine2 &&
                                    formik.errors.linkedFacilities?.[index]
                                      ?.addressLine2 && (
                                      <div className="text-red-500">
                                        {
                                          formik.errors.linkedFacilities?.[
                                            index
                                          ]?.addressLine2
                                        }
                                      </div>
                                    )}
                                </div>
                              </div>

                              <div className="sm:col-span-2">
                                <label
                                  htmlFor={`linkedFacilities.${index}.city`}
                                  className="block text-[16px ]font-medium leading-6 text-gray-900 capitalize required"
                                >
                                  City
                                </label>
                                <div className="mt-2">
                                  <input
                                    name={`linkedFacilities.${index}.city`}
                                    id={`linkedFacilities.${index}.city`}
                                    type="text"
                                    autoComplete="family-name"
                                    {...formik.getFieldProps(
                                      `linkedFacilities.${index}.city`
                                    )}
                                    className="px-3 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                  />
                                  {formik.touched.linkedFacilities?.[index]
                                    ?.city &&
                                    formik.errors.linkedFacilities?.[index]
                                      ?.city && (
                                      <div className="text-red-500">
                                        {
                                          formik.errors.linkedFacilities?.[
                                            index
                                          ]?.city
                                        }
                                      </div>
                                    )}
                                </div>
                              </div>

                              <div className="sm:col-span-2">
                                <label
                                  htmlFor={`linkedFacilities.${index}.state`}
                                  className="block text-[16px ]font-medium leading-6 text-gray-900 capitalize required"
                                >
                                  State
                                </label>
                                <div className="mt-2">
                                  <Select
                                    id={`linkedFacilities.${index}.state`}
                                    name={`linkedFacilities.${index}.state`}
                                    options={indianStates}
                                    value={indianStates.find(
                                      (option) =>
                                        option.value ===
                                        formik.values.linkedFacilities?.[index]
                                          ?.state
                                    )}
                                    onChange={(selectedOption) =>
                                      formik.setFieldValue(
                                        `linkedFacilities.${index}.state`,
                                        selectedOption.value
                                      )
                                    }
                                    onBlur={() =>
                                      formik.setFieldTouched(
                                        `linkedFacilities.${index}.state`,
                                        true
                                      )
                                    }
                                    className="basic-single"
                                    classNamePrefix="select"
                                  />
                                  {formik.touched.linkedFacilities?.[index]
                                    ?.state &&
                                    formik.errors.linkedFacilities?.[index]
                                      ?.state && (
                                      <div className="text-red-500">
                                        {
                                          formik.errors.linkedFacilities?.[
                                            index
                                          ]?.state
                                        }
                                      </div>
                                    )}
                                </div>
                              </div>

                              <div className="sm:col-span-2">
                                <label
                                  htmlFor={`linkedFacilities.${index}.country`}
                                  className="block text-[16px ]font-medium leading-6 text-gray-900 capitalize required"
                                >
                                  Country
                                </label>
                                <div className="mt-2">
                                  <input
                                    type="text"
                                    name={`linkedFacilities.${index}.country`}
                                    id={`linkedFacilities.${index}.country`}
                                    autoComplete="family-name"
                                    {...formik.getFieldProps(
                                      `linkedFacilities.${index}.country`
                                    )}
                                    className="px-3 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                  />
                                  {formik.touched.linkedFacilities?.[index]
                                    ?.country &&
                                    formik.errors.linkedFacilities?.[index]
                                      ?.country && (
                                      <div className="text-red-500">
                                        {
                                          formik.errors.linkedFacilities?.[
                                            index
                                          ]?.country
                                        }
                                      </div>
                                    )}
                                </div>
                              </div>

                              <div className="sm:col-span-6">
                                <label
                                  htmlFor={`linkedFacilities.${index}.amenities`}
                                  className="block text-[16px ]font-medium leading-6 text-gray-900 capitalize"
                                >
                                  Amenities
                                </label>
                                <div className="mt-2">
                                  <ReactQuill
                                    className="border rounded"
                                    value={
                                      formik.values.linkedFacilities[index]
                                        .amenities
                                    }
                                    onChange={(value) => {
                                      formik.setFieldValue(
                                        `linkedFacilities.${index}.amenities`,
                                        value
                                      );
                                    }}
                                  />
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <br />
                      </Fragment>
                    ))}
                  </div>

                  <div className="mt-4">
                    <button
                      type="button"
                      className="rounded-md bg-white px-8 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50"
                      onClick={addAddress}
                    >
                      Add
                    </button>
                  </div>
                </dd>
              </div>
              <div>
                <title>
                  {!isEditable
                    ? "Coach Signup - Professional details"
                    : "Coach Profile - Professional details"}
                </title>

                {verificationModal && (
                  <VerificationBanner
                    verificationModal={verificationModal}
                    setVerificationModal={setVerificationModal}
                    message={message}
                  />
                )}

                <TermsAndConditionModal
                  open={open}
                  setOpen={setOpen}
                  saveData={saveData}
                />

                {successNotification && (
                  <SuccessNotification message={message} />
                )}

                {errorNotification && <ErrorNotification message={message} />}
                <form>
                  <fieldset disabled={!isEditable}>
                    <div className="overflow-hidden bg-white shadow sm:rounded-lg">
                      <div className="border-t border-gray-100">
                        <dl className="divide-y divide-gray-100">
                          <div className="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                            <dt className="text-sm font-semibold tracking-wide text-gray-900 required">
                              Experience
                            </dt>
                            <dd className="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                              <div className="grid grid-cols-1 gap-x-8 gap-y-8  md:grid-cols-1">
                                <div>
                                  <div className="grid max-w-2xl grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
                                    <div className="sm:col-span-6">
                                      <label
                                        htmlFor="experience"
                                        className="block text-[16px ]font-medium leading-6 text-gray-900 capitalize"
                                      >
                                        No. of Years of Experience
                                      </label>
                                      <div className="mt-2">
                                        <input
                                          type="number"
                                          name="experience"
                                          id="experience"
                                          autoComplete="none"
                                          onFocus={(e) =>
                                            e.target.addEventListener(
                                              "wheel",
                                              function (e) {
                                                e.preventDefault();
                                              },
                                              { passive: false }
                                            )
                                          }
                                          placeholder="Enter no. of experience"
                                          className=" px-3 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                          {...formik.getFieldProps(
                                            "experience"
                                          )}
                                        />
                                        <style jsx>{`
                                          input[type="number"]::-webkit-inner-spin-button,
                                          input[type="number"]::-webkit-outer-spin-button {
                                            -webkit-appearance: none;
                                            margin: 0;
                                          }

                                          input[type="number"] {
                                            -moz-appearance: textfield;
                                          }
                                        `}</style>
                                        {formik.touched.experience &&
                                          formik.errors.experience && (
                                            <div className="text-red-500">
                                              {formik.errors.experience}
                                            </div>
                                          )}
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </dd>
                          </div>
                          <div className="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                            <dt className="text-sm font-semibold tracking-wide text-gray-900 required">
                              Language
                            </dt>
                            <dd className="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                              <div className="grid grid-cols-1 gap-x-8 gap-y-8  md:grid-cols-1">
                                <div>
                                  <div className="grid max-w-2xl grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
                                    <div className="sm:col-span-6">
                                      <label
                                        htmlFor="language"
                                        className="block text-[16px ]font-medium leading-6 text-gray-900 capitalize"
                                      >
                                        Language
                                      </label>
                                      <div className="mt-2">
                                        <input
                                          type="text"
                                          name="language"
                                          id="language"
                                          autoComplete="none"
                                          placeholder="Enter languages separated by commma( , )"
                                          className="block w-full rounded-md border-0 py-1.5 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                          {...formik.getFieldProps("language")}
                                        />
                                        {formik.touched.language &&
                                          formik.errors.language && (
                                            <div className="text-red-500">
                                              {formik.errors.language}
                                            </div>
                                          )}
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </dd>
                          </div>
                          <div className="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                            <dt className="text-sm font-semibold tracking-wide text-gray-900 required">
                              Sports Category
                            </dt>
                            <dd className="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                              <div className="grid grid-cols-1 gap-x-8 gap-y-8  md:grid-cols-1">
                                <div>
                                  <div className="grid max-w-2xl grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
                                    <div className="sm:col-span-6">
                                      <MultiSelect
                                        name=" sportsCategories"
                                        disabled={!isEditable}
                                        options={categories?.map(
                                          (category) => ({
                                            label: category.name,
                                            value: category.name,
                                          })
                                        )}
                                        value={selected}
                                        onChange={(e) => {
                                          setSelected(e);
                                          if (e.length > 0) {
                                            let temp = [];
                                            e.map((x) => {
                                              temp.push(x.value);
                                            });
                                            formik.setFieldValue(
                                              "sportsCategories",
                                              temp
                                            );
                                            setCategoryError(false);
                                          } else {
                                            setCategoryError(true);
                                          }
                                        }}
                                        labelledBy="Select the categories"
                                        // {...formik.getFieldProps(" sportsCategories")}
                                        // className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                      />

                                      {categoryError && (
                                        <div className="text-red-500">
                                          Categories are required
                                        </div>
                                      )}
                                      {/* )} */}
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </dd>
                          </div>
                          <div className="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                            <dt className="text-sm font-semibold tracking-wide text-gray-900">
                              Coaching Qualification
                            </dt>
                            <dd className="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                              <div className="grid grid-cols-1 gap-x-8 gap-y-8  md:grid-cols-1">
                                <div>
                                  {formik.values.coachingQualifications.map(
                                    (qualification, index) => (
                                      <div
                                        key={index}
                                        className="grid max-w-2xl grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6"
                                      >
                                        <div className="sm:col-span-4">
                                          <div>
                                            <label
                                              htmlFor={`coachingQualifications.${index}.description`}
                                              className="block text-[16px ]font-medium leading-6 text-gray-900 capitalize flex-row"
                                            >
                                              <span>
                                                Qualification {index + 1}
                                              </span>
                                              {index + 1 > 1 ? (
                                                <button
                                                  type="button"
                                                  className="rounded-full bg-red-600 p-1.5 text-white shadow-sm hover:bg-red-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 float-right"
                                                  onClick={() =>
                                                    formik.setValues(
                                                      (prevState) => ({
                                                        ...prevState,
                                                        coachingQualifications:
                                                          prevState.coachingQualifications.filter(
                                                            (_, i) =>
                                                              i !== index
                                                          ),
                                                      })
                                                    )
                                                  }
                                                >
                                                  <XMarkIcon
                                                    className="h-3 w-3"
                                                    aria-hidden="true"
                                                  />
                                                </button>
                                              ) : null}
                                            </label>
                                            <div className="mt-2">
                                              <textarea
                                                rows={4}
                                                name={`coachingQualifications.${index}.description`}
                                                id={`coachingQualifications.${index}.description`}
                                                className="px-3 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-sky-600 sm:text-sm sm:leading-6"
                                                {...formik.getFieldProps(
                                                  `coachingQualifications.${index}.description`
                                                )}
                                              />
                                              {formik.touched
                                                .coachingQualifications?.[index]
                                                ?.description &&
                                                formik.errors
                                                  .coachingQualifications?.[
                                                  index
                                                ]?.description && (
                                                  <div className="text-red-500">
                                                    {
                                                      formik.errors
                                                        .coachingQualifications?.[
                                                        index
                                                      ]?.description
                                                    }
                                                  </div>
                                                )}
                                            </div>
                                          </div>
                                        </div>
                                        <div className="sm:col-span-2 flex items-end">
                                          <div className="col-span-full bg-white rounded-sm">
                                            <div className="relative block w-full rounded-lg border-2 border-dashed border-gray-300 p-5 text-center hover:border-gray-400">
                                              {formik.values
                                                .coachingQualifications[index]
                                                ?.image === "" && (
                                                <label
                                                  htmlFor={`coachingQualifications.${index}.image`}
                                                  className="cursor-pointer flex flex-col items-center"
                                                  onClick={(e) =>
                                                    e.stopPropagation()
                                                  }
                                                >
                                                  {/* SVG icon */}
                                                  <svg
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    fill="none"
                                                    viewBox="0 0 24 24"
                                                    strokeWidth={1.5}
                                                    stroke="currentColor"
                                                    className="w-8 h-8"
                                                  >
                                                    <path
                                                      strokeLinecap="round"
                                                      strokeLinejoin="round"
                                                      d="m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z"
                                                    />
                                                  </svg>

                                                  <span className=" block text-xs text-gray-500">
                                                    <span className="font-semibold text-blue-500">
                                                      Upload a file
                                                    </span>
                                                    <br /> PNG, JPG, GIF up to
                                                    10MB
                                                  </span>
                                                  <input
                                                    id={`coachingQualifications.${index}.image`}
                                                    type="file"
                                                    name={`coachingQualifications.${index}.image`}
                                                    accept="image/*"
                                                    // value={`coachingQualifications.${index}.image`}
                                                    onChange={(e) =>
                                                      handleFileChange(
                                                        e,
                                                        "coachingQualifications",
                                                        index
                                                      )
                                                    }
                                                    className="hidden"
                                                  />
                                                  {formik.touched
                                                    .coachingQualifications?.[
                                                    index
                                                  ]?.image &&
                                                    formik.errors
                                                      .coachingQualifications?.[
                                                      index
                                                    ]?.image && (
                                                      <div className="text-red-500">
                                                        {
                                                          formik.errors
                                                            .coachingQualifications?.[
                                                            index
                                                          ]?.image
                                                        }
                                                      </div>
                                                    )}
                                                </label>
                                              )}
                                              <div>
                                                {coachingQualificationsImages.length >
                                                  0 &&
                                                  coachingQualificationsImages[
                                                    index
                                                  ]?.image !== "" && (
                                                    <div className="mt-1">
                                                      <div className="relative">
                                                        <img
                                                          src={
                                                            coachingQualificationsImages[
                                                              index
                                                            ]?.image
                                                          }
                                                          alt="Selected Image"
                                                          className={
                                                            coachingQualificationsImages[
                                                              index
                                                            ]?.image &&
                                                            coachingQualificationsImages[
                                                              index
                                                            ].image !== ""
                                                              ? "w-full h-full object-cover rounded"
                                                              : "hidden"
                                                          }
                                                        />
                                                        <button
                                                          onClick={(e) => {
                                                            e.preventDefault();
                                                            formik.setFieldValue(
                                                              `coachingQualifications.${index}.image`,
                                                              ""
                                                            );
                                                            setCoachingQualificationsImages(
                                                              coachingQualificationsImages.filter(
                                                                (x, idx) =>
                                                                  idx !== index
                                                              )
                                                            );
                                                            deleteImageFiles(
                                                              qualification.image,
                                                              "coachingQualifications",
                                                              index
                                                            );
                                                          }}
                                                          className={
                                                            coachingQualificationsImages[
                                                              index
                                                            ]?.image &&
                                                            coachingQualificationsImages[
                                                              index
                                                            ].image !== ""
                                                              ? "absolute top-2 right-2 bg-white p-1 rounded-full hover:bg-gray-200 focus:outline-none z-10"
                                                              : "hidden"
                                                          }
                                                        >
                                                          <svg
                                                            xmlns="http://www.w3.org/2000/svg"
                                                            fill="none"
                                                            viewBox="0 0 24 24"
                                                            stroke="currentColor"
                                                            className="h-4 w-4 text-red-500"
                                                          >
                                                            <path
                                                              strokeLinecap="round"
                                                              strokeLinejoin="round"
                                                              d="M6 18L18 6M6 6l12 12"
                                                            />
                                                          </svg>
                                                        </button>
                                                      </div>
                                                    </div>
                                                  )}
                                              </div>
                                            </div>
                                          </div>
                                        </div>
                                      </div>
                                    )
                                  )}
                                  <div className="mt-2.5">
                                    <button
                                      type="button"
                                      className="rounded-md bg-white px-8 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50"
                                      onClick={addQualification}
                                    >
                                      Add
                                    </button>
                                  </div>
                                </div>
                              </div>
                            </dd>
                          </div>

                          <div className="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                            <dt className="text-sm font-semibold tracking-wide text-gray-900">
                              Coaching Experience
                            </dt>
                            <dd className="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                              <div className="grid grid-cols-1 gap-x-8 gap-y-8  md:grid-cols-1">
                                <div>
                                  {formik.values.coachingExperience.map(
                                    (experience, index) => (
                                      <div
                                        key={index}
                                        className="grid max-w-2xl grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6"
                                      >
                                        <div className="sm:col-span-4">
                                          <div>
                                            <label
                                              htmlFor={`coachingExperience.${index}.description`}
                                              className="block text-[16px ]font-medium leading-6 text-gray-900 capitalize"
                                            >
                                              Experience {index + 1}
                                              {index + 1 > 1 ? (
                                                <button
                                                  type="button"
                                                  className="rounded-full bg-red-600 p-1.5 text-white shadow-sm hover:bg-red-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 float-right"
                                                  onClick={() =>
                                                    formik.setValues(
                                                      (prevState) => ({
                                                        ...prevState,
                                                        coachingExperience:
                                                          prevState.coachingExperience.filter(
                                                            (_, i) =>
                                                              i !== index
                                                          ),
                                                      })
                                                    )
                                                  }
                                                >
                                                  <XMarkIcon
                                                    className="h-3 w-3"
                                                    aria-hidden="true"
                                                  />
                                                </button>
                                              ) : null}
                                            </label>
                                            <div className="mt-2">
                                              <textarea
                                                rows={4}
                                                name={`coachingExperience.${index}.description`}
                                                id={`coachingExperience.${index}.description`}
                                                className="px-3 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-sky-600 sm:text-sm sm:leading-6"
                                                {...formik.getFieldProps(
                                                  `coachingExperience.${index}.description`
                                                )}
                                              />
                                              {formik.touched
                                                .coachingExperience?.[index]
                                                ?.description &&
                                                formik.errors
                                                  .coachingExperience?.[index]
                                                  ?.description && (
                                                  <div className="text-red-500">
                                                    {
                                                      formik.errors
                                                        .coachingExperience?.[
                                                        index
                                                      ]?.description
                                                    }
                                                  </div>
                                                )}
                                            </div>
                                          </div>
                                        </div>
                                        <div className="sm:col-span-2 flex items-end">
                                          <div className="col-span-full bg-white rounded-sm">
                                            <div className="relative block w-full rounded-lg border-2 border-dashed border-gray-300 p-5 text-center hover:border-gray-400">
                                              {formik.values.coachingExperience[
                                                index
                                              ]?.image === "" && (
                                                <label
                                                  htmlFor={`coachingExperience.${index}.image`}
                                                  className="cursor-pointer flex flex-col items-center"
                                                  onClick={(e) =>
                                                    e.stopPropagation()
                                                  }
                                                >
                                                  {/* SVG icon */}
                                                  <svg
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    fill="none"
                                                    viewBox="0 0 24 24"
                                                    strokeWidth={1.5}
                                                    stroke="currentColor"
                                                    className="w-8 h-8"
                                                  >
                                                    <path
                                                      strokeLinecap="round"
                                                      strokeLinejoin="round"
                                                      d="m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z"
                                                    />
                                                  </svg>

                                                  <span className=" block text-xs text-gray-500">
                                                    <span className="font-semibold text-blue-500">
                                                      Upload a file
                                                    </span>
                                                    <br /> PNG, JPG, GIF up to
                                                    10MB
                                                  </span>
                                                  <input
                                                    id={`coachingExperience.${index}.image`}
                                                    type="file"
                                                    name={`coachingExperience.${index}.image`}
                                                    accept="image/*"
                                                    onChange={(e) =>
                                                      handleFileChange(
                                                        e,
                                                        "coachingExperience",
                                                        index
                                                      )
                                                    }
                                                    className="hidden"
                                                  />
                                                </label>
                                              )}
                                              <div>
                                                {coachingExperienceImages.length >
                                                  0 && (
                                                  <div className="mt-1">
                                                    <div className="relative">
                                                      {/* {console.log(index, "index here")} */}
                                                      <img
                                                        src={
                                                          coachingExperienceImages[
                                                            index
                                                          ]?.image
                                                        }
                                                        alt="Selected Image"
                                                        className={
                                                          coachingExperienceImages[
                                                            index
                                                          ]?.image &&
                                                          coachingExperienceImages[
                                                            index
                                                          ].image !== ""
                                                            ? "w-full h-full object-cover rounded"
                                                            : "hidden"
                                                        }
                                                      />
                                                      <button
                                                        onClick={(e) => {
                                                          e.preventDefault();
                                                          formik.setFieldValue(
                                                            `coachingExperience.${index}.image`,
                                                            ""
                                                          );
                                                          setCoachingExperienceImages(
                                                            coachingExperienceImages.filter(
                                                              (x, idx) =>
                                                                idx !== index
                                                            )
                                                          );
                                                          deleteImageFiles(
                                                            experience.image,
                                                            "coachingExperience",
                                                            index
                                                          );
                                                        }}
                                                        className={
                                                          coachingExperienceImages[
                                                            index
                                                          ]?.image &&
                                                          coachingExperienceImages[
                                                            index
                                                          ].image !== ""
                                                            ? "absolute top-2 right-2 bg-white p-1 rounded-full hover:bg-gray-200 focus:outline-none z-10"
                                                            : "hidden"
                                                        }
                                                      >
                                                        <svg
                                                          xmlns="http://www.w3.org/2000/svg"
                                                          fill="none"
                                                          viewBox="0 0 24 24"
                                                          stroke="currentColor"
                                                          className="h-4 w-4 text-red-500"
                                                        >
                                                          <path
                                                            strokeLinecap="round"
                                                            strokeLinejoin="round"
                                                            d="M6 18L18 6M6 6l12 12"
                                                          />
                                                        </svg>
                                                      </button>
                                                    </div>
                                                  </div>
                                                )}
                                              </div>
                                            </div>
                                          </div>
                                        </div>
                                      </div>
                                    )
                                  )}
                                  <div className="mt-2.5">
                                    <button
                                      type="button"
                                      className="rounded-md bg-white px-8 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50"
                                      onClick={addCoachingExperience}
                                    >
                                      Add
                                    </button>
                                  </div>
                                </div>
                              </div>
                            </dd>
                          </div>

                          <div className="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                            <dt className="text-sm font-semibold tracking-wide text-gray-900">
                              Playing Experience
                            </dt>
                            <dd className="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                              <div className="grid grid-cols-1 gap-x-8 gap-y-8  md:grid-cols-1">
                                <div>
                                  {formik.values.playerExperience.map(
                                    (playingExp, index) => (
                                      <div
                                        key={index}
                                        className="grid max-w-2xl grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6"
                                      >
                                        <div className="sm:col-span-4">
                                          <div>
                                            <label
                                              htmlFor={`playerExperience.${index}.description`}
                                              className="block text-[16px ]font-medium leading-6 text-gray-900 capitalize"
                                            >
                                              Playing Experience {index + 1}
                                              {index + 1 > 1 ? (
                                                <button
                                                  type="button"
                                                  className="rounded-full bg-red-600 p-1.5 text-white shadow-sm hover:bg-red-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 float-right"
                                                  onClick={() =>
                                                    formik.setValues(
                                                      (prevState) => ({
                                                        ...prevState,
                                                        playerExperience:
                                                          prevState.playerExperience.filter(
                                                            (_, i) =>
                                                              i !== index
                                                          ),
                                                      })
                                                    )
                                                  }
                                                >
                                                  <XMarkIcon
                                                    className="h-3 w-3"
                                                    aria-hidden="true"
                                                  />
                                                </button>
                                              ) : null}
                                            </label>
                                            <div className="mt-2">
                                              <textarea
                                                rows={4}
                                                name={`playerExperience.${index}.description`}
                                                id={`playerExperience.${index}.description`}
                                                className="px-3 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-sky-600 sm:text-sm sm:leading-6"
                                                {...formik.getFieldProps(
                                                  `playerExperience.${index}.description`
                                                )}
                                              />
                                              {formik.touched
                                                .playerExperience?.[index]
                                                ?.description &&
                                                formik.errors
                                                  .playerExperience?.[index]
                                                  ?.description && (
                                                  <div className="text-red-500">
                                                    {
                                                      formik.errors
                                                        .playerExperience?.[
                                                        index
                                                      ]?.description
                                                    }
                                                  </div>
                                                )}
                                            </div>
                                          </div>
                                        </div>
                                        <div className="sm:col-span-2 flex items-end">
                                          <div className="col-span-full bg-white rounded-sm">
                                            <div className="relative block w-full rounded-lg border-2 border-dashed border-gray-300 p-5 text-center hover:border-gray-400">
                                              {formik.values.playerExperience[
                                                index
                                              ]?.image === "" && (
                                                <label
                                                  htmlFor="imageInput"
                                                  className="cursor-pointer flex flex-col items-center"
                                                  onClick={(e) =>
                                                    e.stopPropagation()
                                                  }
                                                >
                                                  {/* SVG icon */}
                                                  <svg
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    fill="none"
                                                    viewBox="0 0 24 24"
                                                    strokeWidth={1.5}
                                                    stroke="currentColor"
                                                    className="w-8 h-8"
                                                  >
                                                    <path
                                                      strokeLinecap="round"
                                                      strokeLinejoin="round"
                                                      d="m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z"
                                                    />
                                                  </svg>

                                                  <span className=" block text-xs text-gray-500">
                                                    <span className="font-semibold text-blue-500">
                                                      Upload a file
                                                    </span>
                                                    <br /> PNG, JPG, GIF up to
                                                    10MB
                                                  </span>
                                                  <input
                                                    id="imageInput"
                                                    type="file"
                                                    accept="image/*"
                                                    onChange={(e) =>
                                                      handleFileChange(
                                                        e,
                                                        "playerExperience",
                                                        index
                                                      )
                                                    }
                                                    className="hidden"
                                                  />
                                                </label>
                                              )}
                                              <div>
                                                {playingExperienceImages.length >
                                                  0 && (
                                                  <div className="mt-1">
                                                    {/* {console.log(
                                            playingExperienceImages,
                                            "pp"
                                          )} */}
                                                    <div className="relative">
                                                      <img
                                                        src={
                                                          playingExperienceImages[
                                                            index
                                                          ]?.image
                                                        }
                                                        alt="Selected Image"
                                                        className={
                                                          playingExperienceImages[
                                                            index
                                                          ]?.image &&
                                                          playingExperienceImages[
                                                            index
                                                          ].image !== ""
                                                            ? "w-full h-full object-cover rounded"
                                                            : "hidden"
                                                        }
                                                      />
                                                      <button
                                                        onClick={(e) => {
                                                          e.preventDefault();
                                                          formik.setFieldValue(
                                                            `playerExperience.${index}.image`,
                                                            ""
                                                          );
                                                          setPlayingExperienceImages(
                                                            playingExperienceImages.filter(
                                                              (x, idx) =>
                                                                idx !== index
                                                            )
                                                          );
                                                          deleteImageFiles(
                                                            playingExp.image,
                                                            "playerExperience",
                                                            index
                                                          );
                                                        }}
                                                        className={
                                                          playingExperienceImages[
                                                            index
                                                          ]?.image &&
                                                          playingExperienceImages[
                                                            index
                                                          ].image !== ""
                                                            ? "absolute top-2 right-2 bg-white p-1 rounded-full hover:bg-gray-200 focus:outline-none z-10"
                                                            : "hidden"
                                                        }
                                                      >
                                                        <svg
                                                          xmlns="http://www.w3.org/2000/svg"
                                                          fill="none"
                                                          viewBox="0 0 24 24"
                                                          stroke="currentColor"
                                                          className="h-4 w-4 text-red-500"
                                                        >
                                                          <path
                                                            strokeLinecap="round"
                                                            strokeLinejoin="round"
                                                            d="M6 18L18 6M6 6l12 12"
                                                          />
                                                        </svg>
                                                      </button>
                                                    </div>
                                                  </div>
                                                )}
                                              </div>
                                            </div>
                                          </div>
                                        </div>
                                      </div>
                                    )
                                  )}
                                  <div className="mt-2.5">
                                    <button
                                      type="button"
                                      className="rounded-md bg-white px-8 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50"
                                      onClick={addPlayingExperience}
                                    >
                                      Add
                                    </button>
                                  </div>
                                </div>
                              </div>
                            </dd>
                          </div>

                          <div className="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                            <dt className="text-sm font-semibold tracking-wide text-gray-900">
                              Award
                            </dt>
                            <dd className="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                              <div className="grid grid-cols-1 gap-x-8 gap-y-8  md:grid-cols-1">
                                <div>
                                  {formik.values.award.map((awrd, index) => (
                                    <div
                                      key={index}
                                      className="grid max-w-2xl grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6"
                                    >
                                      <div className="sm:col-span-4">
                                        <div>
                                          <label
                                            htmlFor={`award.${index}.description`}
                                            className="block text-[16px ]font-medium leading-6 text-gray-900 capitalize"
                                          >
                                            Award {index + 1}
                                            {index + 1 > 1 ? (
                                              <button
                                                type="button"
                                                className="rounded-full bg-red-600 p-1.5 text-white shadow-sm hover:bg-red-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 float-right"
                                                onClick={() =>
                                                  formik.setValues(
                                                    (prevState) => ({
                                                      ...prevState,
                                                      award:
                                                        prevState.award.filter(
                                                          (_, i) => i !== index
                                                        ),
                                                    })
                                                  )
                                                }
                                              >
                                                <XMarkIcon
                                                  className="h-3 w-3"
                                                  aria-hidden="true"
                                                />
                                              </button>
                                            ) : null}
                                          </label>
                                          <div className="mt-2">
                                            <textarea
                                              rows={4}
                                              name={`award.${index}.description`}
                                              id={`award.${index}.description`}
                                              className="px-3 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-sky-600 sm:text-sm sm:leading-6"
                                              {...formik.getFieldProps(
                                                `award.${index}.description`
                                              )}
                                            />
                                            {formik.touched.award?.[index]
                                              ?.description &&
                                              formik.errors.award?.[index]
                                                ?.description && (
                                                <div className="text-red-500">
                                                  {
                                                    formik.errors.award?.[index]
                                                      ?.description
                                                  }
                                                </div>
                                              )}
                                          </div>
                                        </div>
                                      </div>
                                      <div className="sm:col-span-2 flex items-end">
                                        <div className="col-span-full bg-white rounded-sm">
                                          <div className="relative block w-full rounded-lg border-2 border-dashed border-gray-300 p-5 text-center hover:border-gray-400">
                                            {formik.values.award[index]
                                              ?.image === "" && (
                                              <label
                                                htmlFor={`award.${index}.image`}
                                                className="cursor-pointer flex flex-col items-center"
                                                onClick={(e) =>
                                                  e.stopPropagation()
                                                }
                                              >
                                                {/* SVG icon */}
                                                <svg
                                                  xmlns="http://www.w3.org/2000/svg"
                                                  fill="none"
                                                  viewBox="0 0 24 24"
                                                  strokeWidth={1.5}
                                                  stroke="currentColor"
                                                  className="w-8 h-8"
                                                >
                                                  <path
                                                    strokeLinecap="round"
                                                    strokeLinejoin="round"
                                                    d="m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z"
                                                  />
                                                </svg>

                                                <span className=" block text-xs text-gray-500">
                                                  <span className="font-semibold text-blue-500">
                                                    Upload a file
                                                  </span>
                                                  <br /> PNG, JPG, GIF up to
                                                  10MB
                                                </span>
                                                <input
                                                  id={`award.${index}.image`}
                                                  type="file"
                                                  name={`award.${index}.image`}
                                                  accept="image/*"
                                                  onChange={(e) =>
                                                    handleFileChange(
                                                      e,
                                                      "award",
                                                      index
                                                    )
                                                  }
                                                  className="hidden"
                                                  // {...formik.getFieldProps(
                                                  //   `awards.${index}.image`
                                                  // )}
                                                />
                                                {formik.touched.award?.[index]
                                                  ?.image &&
                                                  formik.errors.award?.[index]
                                                    ?.image && (
                                                    <div className="text-red-500">
                                                      {
                                                        formik.errors.award?.[
                                                          index
                                                        ]?.image
                                                      }
                                                    </div>
                                                  )}
                                              </label>
                                            )}
                                            <div>
                                              {awardImages.length > 0 && (
                                                <div className="mt-1">
                                                  <div className="relative">
                                                    <img
                                                      src={
                                                        awardImages[index]
                                                          ?.image
                                                      }
                                                      alt="Selected Image"
                                                      className={
                                                        awardImages[index]
                                                          ?.image &&
                                                        awardImages[index]
                                                          ?.image !== ""
                                                          ? "w-full h-full object-cover rounded"
                                                          : "hidden"
                                                      }
                                                    />
                                                    <button
                                                      onClick={(e) => {
                                                        e.preventDefault();
                                                        formik.setFieldValue(
                                                          `award.${index}.image`,
                                                          ""
                                                        );
                                                        setAwardImages(
                                                          awardImages.filter(
                                                            (x, idx) =>
                                                              idx !== index
                                                          )
                                                        );
                                                        deleteImageFiles(
                                                          awrd.image,
                                                          "award",
                                                          index
                                                        );
                                                      }}
                                                      className={
                                                        awardImages[index]
                                                          ?.image &&
                                                        awardImages[index]
                                                          ?.image !== ""
                                                          ? "absolute top-2 right-2 bg-white p-1 rounded-full hover:bg-gray-200 focus:outline-none z-10"
                                                          : "hidden"
                                                      }
                                                    >
                                                      <svg
                                                        xmlns="http://www.w3.org/2000/svg"
                                                        fill="none"
                                                        viewBox="0 0 24 24"
                                                        stroke="currentColor"
                                                        className="h-4 w-4 text-red-500"
                                                      >
                                                        <path
                                                          strokeLinecap="round"
                                                          strokeLinejoin="round"
                                                          d="M6 18L18 6M6 6l12 12"
                                                        />
                                                      </svg>
                                                    </button>
                                                  </div>
                                                </div>
                                              )}
                                            </div>
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  ))}
                                  <div className="mt-2.5">
                                    <button
                                      type="button"
                                      className="rounded-md bg-white px-8 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50"
                                      onClick={addAwards}
                                    >
                                      Add
                                    </button>
                                  </div>
                                </div>
                              </div>
                            </dd>
                          </div>
                        </dl>
                      </div>
                    </div>
                  </fieldset>
                </form>
              </div>

              <div>
                <title>
                  {isEditable
                    ? "Coach Signup - Kyc details"
                    : "Coach Profile - Kyc details"}
                </title>

                {verificationModal && (
                  <VerificationBanner
                    verificationModal={verificationModal}
                    setVerificationModal={setVerificationModal}
                    message={message}
                  />
                )}

                {/* <ConfirmaitonModal open={open} setOpen={setOpen} saveData={saveData} /> */}

                {successNotification && (
                  <SuccessNotification message={message} />
                )}

                {errorNotification && <ErrorNotification message={message} />}
                <form>
                  <fieldset disabled={!isEditable}>
                    <div className="overflow-hidden bg-white shadow sm:rounded-lg">
                      <div className="border-t border-gray-100">
                        <dl className="divide-y divide-gray-100">
                          <div className="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                            <dt className="text-sm font-semibold tracking-wide text-gray-900">
                              KYC Details
                            </dt>
                            <dd className="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                              <div className="grid grid-cols-1 gap-x-8 gap-y-8  md:grid-cols-1">
                                <div>
                                  <div className="grid max-w-2xl grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
                                    <div className="sm:col-span-6">
                                      <label
                                        htmlFor="kycDocuments.documentNumber"
                                        className="block text-[16px ]font-medium leading-6 text-gray-900 capitalize"
                                      >
                                        PAN Number
                                      </label>
                                      <div className="mt-2">
                                        <input
                                          type="text"
                                          name="kycDocuments.documentNumber"
                                          id="kycDocuments.documentNumber"
                                          autoComplete="none"
                                          onInput={(e) => {
                                            e.target.value =
                                              e.target.value.toUpperCase();
                                          }}
                                          placeholder="Enter PAN No."
                                          className="block w-full rounded-md border-0 py-1.5 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                          {...formik.getFieldProps(
                                            "kycDocuments.documentNumber"
                                          )}
                                        />
                                        {formik.touched.kycDocuments
                                          ?.documentNumber &&
                                          formik.errors.kycDocuments
                                            ?.documentNumber && (
                                            <div className="text-red-500">
                                              {
                                                formik.errors.kycDocuments
                                                  ?.documentNumber
                                              }
                                            </div>
                                          )}
                                      </div>
                                      <div className="grid max-w-2xl mt-4 grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
                                        <div className="col-span-full bg-white rounded-sm sm:col-span-2">
                                          <div className="relative block w-full rounded-lg border-2 border-dashed border-gray-300 p-5 text-center hover:border-gray-400">
                                            {selectedImages.length === 0 && (
                                              <label
                                                htmlFor={`kycDocuments.documentImg.${0}.url`}
                                                className="cursor-pointer flex flex-col items-center"
                                                onClick={(e) =>
                                                  e.stopPropagation()
                                                }
                                              >
                                                {/* SVG icon */}
                                                <svg
                                                  xmlns="http://www.w3.org/2000/svg"
                                                  fill="none"
                                                  viewBox="0 0 24 24"
                                                  strokeWidth={1.5}
                                                  stroke="currentColor"
                                                  className="w-8 h-8"
                                                >
                                                  <path
                                                    strokeLinecap="round"
                                                    strokeLinejoin="round"
                                                    d="m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z"
                                                  />
                                                </svg>

                                                <span className=" block text-xs text-gray-500">
                                                  <span className="font-semibold text-blue-500">
                                                    Upload a file
                                                  </span>
                                                  <br /> PNG, JPG, GIF up to
                                                  10MB
                                                </span>
                                                <input
                                                  id={`kycDocuments.documentImg.${0}.url`}
                                                  name={`kycDocuments.documentImg.${0}.url`}
                                                  type="file"
                                                  disabled={!isEditable}
                                                  accept="image/*"
                                                  onChange={(e) =>
                                                    handleFileChangeImage(e, 0)
                                                  }
                                                  className="hidden"
                                                />
                                                {formik.touched.kycDocuments
                                                  ?.documentImg?.[0]?.url &&
                                                  formik.errors.kycDocuments
                                                    ?.documentImg?.[0]?.url && (
                                                    <div className="text-red-500">
                                                      {
                                                        formik.errors
                                                          .kycDocuments
                                                          ?.documentImg?.[0]
                                                          ?.url
                                                      }
                                                    </div>
                                                  )}
                                              </label>
                                            )}
                                            <div>
                                              {selectedImages.length > 0 && (
                                                <div className="mt-1">
                                                  <div className="relative">
                                                    <img
                                                      src={
                                                        selectedImages[0]?.url
                                                      }
                                                      alt="Selected Image"
                                                      className="w-full h-full object-cover rounded"
                                                    />
                                                    <button
                                                      disabled={!isEditable}
                                                      onClick={async (e) => {
                                                        e.preventDefault();
                                                        await deleteFilesImage(
                                                          formik.values
                                                            .kycDocuments
                                                            .documentImg[0].url,
                                                          0
                                                        );
                                                        formik.setFieldValue(
                                                          `kycDocuments.documentImg.${0}.url`,
                                                          ""
                                                        );
                                                      }}
                                                      className="absolute top-2 right-2 bg-white p-1 rounded-full hover:bg-gray-200 focus:outline-none z-10"
                                                    >
                                                      <svg
                                                        xmlns="http://www.w3.org/2000/svg"
                                                        fill="none"
                                                        viewBox="0 0 24 24"
                                                        stroke="currentColor"
                                                        className="h-4 w-4 text-red-500"
                                                      >
                                                        <path
                                                          strokeLinecap="round"
                                                          strokeLinejoin="round"
                                                          d="M6 18L18 6M6 6l12 12"
                                                        />
                                                      </svg>
                                                    </button>
                                                  </div>
                                                </div>
                                              )}
                                            </div>
                                          </div>
                                        </div>
                                        <div className="col-span-full bg-white rounded-sm sm:col-span-2">
                                          {selectedImages.length >= 1 && (
                                            <div className="relative block w-full rounded-lg border-2 border-dashed border-gray-300 p-5 text-center hover:border-gray-400">
                                              {selectedImages.length === 1 && (
                                                <label
                                                  htmlFor={`kycDocuments.documentImg.${1}.url`}
                                                  className="cursor-pointer flex flex-col items-center"
                                                  onClick={(e) =>
                                                    e.stopPropagation()
                                                  }
                                                >
                                                  {/* SVG icon */}
                                                  <svg
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    fill="none"
                                                    viewBox="0 0 24 24"
                                                    strokeWidth={1.5}
                                                    stroke="currentColor"
                                                    className="w-8 h-8"
                                                  >
                                                    <path
                                                      strokeLinecap="round"
                                                      strokeLinejoin="round"
                                                      d="m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z"
                                                    />
                                                  </svg>

                                                  <span className=" block text-xs text-gray-500">
                                                    <span className="font-semibold text-blue-500">
                                                      Upload a file
                                                    </span>
                                                    <br /> PNG, JPG, GIF up to
                                                    10MB
                                                  </span>
                                                  <input
                                                    id={`kycDocuments.documentImg.${1}.url`}
                                                    name={`kycDocuments.documentImg.${1}.url`}
                                                    type="file"
                                                    disabled={!isEditable}
                                                    accept="image/*"
                                                    onChange={(e) =>
                                                      handleFileChangeImage(
                                                        e,
                                                        1
                                                      )
                                                    }
                                                    className="hidden"
                                                  />
                                                  {formik.touched.kycDocuments
                                                    ?.documentImg?.[1]?.url &&
                                                    formik.errors.kycDocuments
                                                      ?.documentImg?.[1]
                                                      ?.url && (
                                                      <div className="text-red-500">
                                                        {
                                                          formik.errors
                                                            .kycDocuments
                                                            ?.documentImg?.[1]
                                                            ?.url
                                                        }
                                                      </div>
                                                    )}
                                                </label>
                                              )}
                                              <div>
                                                {selectedImages.length > 1 && (
                                                  <div className="mt-1">
                                                    {selectedImages[1]?.url &&
                                                      selectedImages[1]?.url !==
                                                        "" && (
                                                        <div className="relative">
                                                          <img
                                                            src={
                                                              selectedImages[1]
                                                                ?.url
                                                            }
                                                            alt="Selected Image"
                                                            className="w-full h-full object-cover rounded"
                                                          />
                                                          <button
                                                            disabled={
                                                              !isEditable
                                                            }
                                                            onClick={async (
                                                              e
                                                            ) => {
                                                              e.preventDefault();
                                                              await deleteFilesImage(
                                                                formik.values
                                                                  .kycDocuments
                                                                  .documentImg[1]
                                                                  .url,
                                                                1
                                                              );
                                                              formik.setFieldValue(
                                                                `kycDocuments.documentImg.${1}.url`,
                                                                ""
                                                              );
                                                            }}
                                                            className="absolute top-2 right-2 bg-white p-1 rounded-full hover:bg-gray-200 focus:outline-none z-10"
                                                          >
                                                            <svg
                                                              xmlns="http://www.w3.org/2000/svg"
                                                              fill="none"
                                                              viewBox="0 0 24 24"
                                                              stroke="currentColor"
                                                              className="h-4 w-4 text-red-500"
                                                            >
                                                              <path
                                                                strokeLinecap="round"
                                                                strokeLinejoin="round"
                                                                d="M6 18L18 6M6 6l12 12"
                                                              />
                                                            </svg>
                                                          </button>
                                                        </div>
                                                      )}
                                                  </div>
                                                )}
                                              </div>
                                            </div>
                                          )}
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </dd>
                          </div>
                          <div className="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                            <dt className="text-sm font-semibold tracking-wide text-gray-900">
                              Account Details
                            </dt>
                            <dd className="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                              <div className="grid grid-cols-1 gap-x-8 gap-y-8  md:grid-cols-1">
                                <div>
                                  <div className="grid max-w-2xl grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
                                    <div className="sm:col-span-3">
                                      <label
                                        htmlFor="bankDetails.accountHolderName"
                                        className="block text-[16px ]font-medium leading-6 text-gray-900 capitalize"
                                      >
                                        Account Holder Name
                                      </label>
                                      <div className="mt-2">
                                        <input
                                          type="text"
                                          name="bankDetails.accountHolderName"
                                          id="bankDetails.accountHolderName"
                                          autoComplete="none"
                                          placeholder="Enter name"
                                          {...formik.getFieldProps(
                                            "bankDetails.accountHolderName"
                                          )}
                                          className="block w-full rounded-md border-0 py-1.5 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                        />
                                        {formik.touched.bankDetails
                                          ?.accountHolderName &&
                                          formik.errors.bankDetails
                                            ?.accountHolderName && (
                                            <div className="text-red-500">
                                              {
                                                formik.errors.bankDetails
                                                  ?.accountHolderName
                                              }
                                            </div>
                                          )}
                                      </div>
                                    </div>
                                    <div className="sm:col-span-3">
                                      <label
                                        htmlFor="bankDetails.accountNumber"
                                        className="block text-[16px ]font-medium leading-6 text-gray-900 capitalize"
                                      >
                                        Account No.
                                      </label>
                                      <div className="mt-2">
                                        <input
                                          type="number"
                                          name="bankDetails.accountNumber"
                                          id="bankDetails.accountNumber"
                                          maxLength={10}
                                          max={10}
                                          autoComplete="none"
                                          onFocus={(e) =>
                                            e.target.addEventListener(
                                              "wheel",
                                              function (e) {
                                                e.preventDefault();
                                              },
                                              { passive: false }
                                            )
                                          }
                                          placeholder="Enter account no."
                                          {...formik.getFieldProps(
                                            "bankDetails.accountNumber"
                                          )}
                                          className="block w-full rounded-md border-0 py-1.5 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                        />
                                        <style jsx>{`
                                          input[type="number"]::-webkit-inner-spin-button,
                                          input[type="number"]::-webkit-outer-spin-button {
                                            -webkit-appearance: none;
                                            margin: 0;
                                          }

                                          input[type="number"] {
                                            -moz-appearance: textfield;
                                          }
                                        `}</style>
                                        {formik.touched.bankDetails
                                          ?.accountNumber &&
                                          formik.errors.bankDetails
                                            ?.accountNumber && (
                                            <div className="text-red-500">
                                              {
                                                formik.errors.bankDetails
                                                  ?.accountNumber
                                              }
                                            </div>
                                          )}
                                      </div>
                                    </div>
                                    <div className="sm:col-span-3">
                                      <label
                                        htmlFor="bankDetails.ifsc"
                                        className="block text-[16px ]font-medium leading-6 text-gray-900 capitalize"
                                      >
                                        IFSC Code
                                      </label>
                                      <div className="mt-2">
                                        <input
                                          type="text"
                                          name="bankDetails.ifsc"
                                          id="bankDetails.ifsc"
                                          autoComplete="none"
                                          placeholder="Enter IFSC code"
                                          {...formik.getFieldProps(
                                            "bankDetails.ifsc"
                                          )}
                                          className="block w-full rounded-md border-0 py-1.5 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                        />
                                        {formik.touched.bankDetails?.ifsc &&
                                          formik.errors.bankDetails?.ifsc && (
                                            <div className="text-red-500">
                                              {formik.errors.bankDetails?.ifsc}
                                            </div>
                                          )}
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </dd>
                          </div>

                          <div className="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                            <dt className="text-sm font-semibold tracking-wide text-gray-900">
                              GST Details
                            </dt>
                            <dd className="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                              <div className="grid grid-cols-1 gap-x-8 gap-y-8  md:grid-cols-1">
                                <div>
                                  <div className="grid max-w-2xl grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
                                    <div className="col-span-full">
                                      <div className="flex gap-4 items-center">
                                        <label
                                          htmlFor="hasGst"
                                          className="mb-2 block text-[16px ]font-medium leading-6 text-gray-900 capitalize"
                                        >
                                          Has GST ?
                                        </label>
                                        <div className="flex items-center gap-1">
                                          <input
                                            type="radio"
                                            id="yes"
                                            name="hasGst"
                                            checked={formik.values.hasGst}
                                            // disabled={!isEditable}
                                            value="yes"
                                            onChange={(e) =>
                                              formik.setFieldValue(
                                                "hasGst",
                                                e.target.value == "yes"
                                                  ? true
                                                  : false
                                              )
                                            }
                                          />
                                          <label
                                            htmlFor="yes"
                                            className="text-sm text-gray-700"
                                          >
                                            Yes
                                          </label>
                                        </div>
                                        <div className="flex items-center gap-1">
                                          <input
                                            type="radio"
                                            id="no"
                                            // disabled={!isEditable}
                                            name="hasGst"
                                            checked={!formik.values.hasGst}
                                            value="no"
                                            onChange={(e) =>
                                              formik.setFieldValue(
                                                "hasGst",
                                                e.target.value == "yes"
                                                  ? true
                                                  : false
                                              )
                                            }
                                          />
                                          <label
                                            htmlFor="no"
                                            className="text-sm text-gray-700"
                                          >
                                            No
                                          </label>
                                        </div>
                                      </div>
                                    </div>
                                    {formik.values.hasGst && (
                                      <div className="sm:col-span-3">
                                        <label
                                          htmlFor="gstNumber"
                                          className="block text-[16px ]font-medium leading-6 text-gray-900 capitalize"
                                        >
                                          GST Account No.
                                        </label>
                                        <div className="mt-2">
                                          <input
                                            type="text"
                                            name="gstNumber"
                                            id="gstNumber"
                                            autoComplete="none"
                                            placeholder="Enter GST number."
                                            {...formik.getFieldProps(
                                              "gstNumber"
                                            )}
                                            className="block w-full rounded-md border-0 py-1.5 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                          />
                                          {formik.touched.gstNumber &&
                                            formik.errors.gstNumber && (
                                              <div className="text-red-500">
                                                {formik.errors.gstNumber}
                                              </div>
                                            )}
                                        </div>
                                      </div>
                                    )}
                                    {formik.values.hasGst && (
                                      <div className="sm:col-span-3">
                                        <label
                                          htmlFor="gstState"
                                          className="block text-[16px] font-medium leading-6 text-gray-900 capitalize required"
                                        >
                                          GST state
                                        </label>
                                        <div className="mt-2">
                                          <select
                                            name="gstState"
                                            disabled={!isEditable}
                                            id="gstState"
                                            autoComplete="address-level1"
                                            {...formik.getFieldProps(
                                              "gstState"
                                            )}
                                            className="px-3 block w-full rounded-md border-0 py-1.5 h-9 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                          >
                                            <option value="">
                                              Select State
                                            </option>
                                            {states.map((state) => (
                                              <option
                                                key={state.isoCode}
                                                value={state.isoCode}
                                              >
                                                {state.name}
                                              </option>
                                            ))}
                                          </select>
                                          {formik.touched.gstState &&
                                            formik.errors.gstState && (
                                              <div className="text-red-500">
                                                {formik.errors.gstState}
                                              </div>
                                            )}
                                        </div>
                                      </div>
                                    )}
                                  </div>
                                </div>
                              </div>
                            </dd>
                          </div>
                        </dl>
                      </div>
                    </div>
                  </fieldset>
                </form>
              </div>
              <div className="mt-3 flex items-center justify-end gap-x-6 p-4">
                <button
                  type="button"
                  onClick={() => {
                    showEdit ? router.push("/calendar") : router.push("/login");
                  }}
                  className="text-sm font-semibold leading-6 text-gray-900"
                >
                  Cancel
                </button>
                {loading ? (
                  <button
                    disabled
                    type="button"
                    className="text-white px-3 py-2.5 bg-indigo-700 hover:bg-blue-800   font-medium rounded text-sm text-center mr-2 dark:bg-indigo-600 dark:hover:bg-indigo-700  inline-flex items-center"
                  >
                    <svg
                      aria-hidden="true"
                      role="status"
                      class="inline mr-3 w-4 h-4 text-white animate-spin"
                      viewBox="0 0 100 101"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                        fill="#E5E7EB"
                      ></path>
                      <path
                        d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                        fill="currentColor"
                      ></path>
                    </svg>
                    Loading...
                  </button>
                ) : (
                  <button
                    type="submit"
                    // disabled={formik.dirty}
                    className="rounded-md bg-sky-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-sky-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-sky-600"
                    // onClick={formik.handleSubmit}
                    onClick={(e) => {
                      e.preventDefault();
                      handlePolicyModal(formik);
                    }}
                  >
                    Save
                  </button>
                )}
              </div>
            </dl>
          </div>
        </div>
        {/* </fieldset> */}
      </form>
    </>
  );
}

function DeleteDialog({
  showDeleteModal,
  setShowDeleteModal,
  deleteImageFiles,
  profile,
  loading,
}) {
  return (
    <>
      <Transition.Root show={showDeleteModal} as={Fragment}>
        <Dialog
          as="div"
          className="relative z-10"
          onClose={() => {
            setShowDeleteModal(false);
          }}
        >
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
          </Transition.Child>

          <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
            <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                enterTo="opacity-100 translate-y-0 sm:scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 translate-y-0 sm:scale-100"
                leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              >
                <Dialog.Panel className="relative transform overflow-hidden rounded-lg mb-[70%] bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-sm sm:p-6">
                  <div>
                    <div className="mt-3 text-center sm:mt-5">
                      <Dialog.Title
                        as="h3"
                        className="text-base font-semibold leading-6 text-gray-900"
                      >
                        {"Delete Image ?"}
                      </Dialog.Title>
                      <div className="mt-2">
                        <p className="text-sm text-gray-500">
                          Do you really want to delete the profile image?
                        </p>
                      </div>
                    </div>
                  </div>
                  <div className="mt-5 sm:mt-6 flex justify-end gap-7">
                    <button
                      type="button"
                      className="inline-flex justify-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
                      onClick={() => setShowDeleteModal(false)}
                    >
                      Cancel
                    </button>
                    {loading ? (
                      <button
                        disabled
                        type="button"
                        className="text-white px-3 py-2.5 bg-red-500 hover:bg-red-500 font-medium rounded text-sm text-center mr-2 dark:bg-red-600 dark:hover:bg-red-500 inline-flex items-center"
                      >
                        <svg
                          aria-hidden="true"
                          role="status"
                          class="inline mr-3 w-4 h-4 text-white animate-spin"
                          viewBox="0 0 100 101"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                            fill="#E5E7EB"
                          ></path>
                          <path
                            d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                            fill="currentColor"
                          ></path>
                        </svg>
                        Loading...
                      </button>
                    ) : (
                      <button
                        type="button"
                        className="inline-flex justify-center rounded-md bg-red-500 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-red-600 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-red-600"
                        onClick={() => deleteImageFiles(profile)}
                      >
                        Delete
                      </button>
                    )}
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition.Root>
    </>
  );
}
