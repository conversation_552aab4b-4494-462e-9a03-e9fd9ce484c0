import axios from "axios";
import { NextResponse } from "next/server";
import API from "@/components/API";

export async function POST(req) {
  try {
    const reqBody = await req.json();

    const response = await axios.post(
      `${API}/api/coach/resetPassword`,
      reqBody,
      {
        headers: { "Content-Type": "application/json" },
      }
    );

    return new NextResponse(JSON.stringify(response.data));
  } catch (error) {
    console.error(error.response);
    return new NextResponse(JSON.stringify({ error: error }));
  }
}
